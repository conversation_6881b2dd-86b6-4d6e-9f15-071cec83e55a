import requests
from typing import List, Dict, Any, Optional
import aiohttp
from langchain_core.documents import Document
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TEIReranker:
    def __init__(self, tei_url: str = "http://localhost:8080", timeout: int = 30):
        """
        Initialize the TEI reranker client with both sync and async support.

        Args:
            tei_url: URL of your deployed TEI service
            timeout: Request timeout in seconds
        """
        self.tei_url: str = tei_url.rstrip("/")
        if self.tei_url.endswith("/v1"):
            self.tei_url = self.tei_url.split("/v1")[0]
        self.rerank_endpoint = f"{self.tei_url}/rerank"
        self.timeout = timeout

    def compress_documents(
        self,
        documents: List[Document],
        query: str,
    ) -> List[Document]:
        docs = self.rerank(
            query=query, documents=[doc.page_content for doc in documents]
        )
        return [
            Document(
                page_content=doc["document"],
                metadata={
                    **documents[doc["original_index"]].metadata,
                    "relevance_score": doc["score"],
                },
            )
            for doc in docs
        ]

    async def acompress_documents(
        self,
        documents: List[Document],
        query: str,
    ) -> List[Document]:
        docs = self.arerank(
            query=query, documents=[doc.page_content for doc in documents]
        )
        return [
            Document(
                page_content=doc["document"],
                metadata={
                    **documents[doc["original_index"]].metadata,
                    "relevance_score": doc["score"],
                },
            )
            for doc in docs
        ]

    def rerank(
        self, query: str, documents: List[str], top_k: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Synchronous method to rerank documents using TEI service.
        """
        payload = self._create_payload(query, documents, top_k)
        if len(payload["query"]) <= 0 or len(payload["texts"]) <= 0:
            logger.warning("Empty query or documents list.")
            return []

        try:
            response = requests.post(
                self.rerank_endpoint,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=self.timeout,
            )
            response.raise_for_status()
            return self._process_response(response.json(), documents)

        except requests.exceptions.RequestException as e:
            # raise RuntimeError(f"Sync request failed: {str(e)}")
            logger.exception(f"Sync request failed: {str(e)}")
            return []
        except KeyError as e:
            # raise RuntimeError(f"Invalid response format: {str(e)}")
            logger.exception(f"Invalid response format: {str(e)}")
            return []

    async def arerank(
        self, query: str, documents: List[str], top_k: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Asynchronous method to rerank documents using TEI service.
        """
        payload = self._create_payload(query, documents, top_k)
        if len(payload["query"]) <= 0 or len(payload["texts"]) <= 0:
            logger.warning("Empty query or documents list.")
            return []

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.rerank_endpoint,
                    json=payload,
                    headers={"Content-Type": "application/json"},
                    timeout=self.timeout,
                ) as response:
                    response.raise_for_status()
                    response_data = await response.json()
                    return self._process_response(response_data, documents)

        except aiohttp.ClientError as e:
            logger.exception(f"Async request failed: {str(e)}")
            return []
        except KeyError as e:
            # raise RuntimeError(f"Invalid response format: {str(e)}")
            logger.exception(f"Invalid response format: {str(e)}")
            return []

    def _create_payload(
        self, query: str, documents: List[str], top_k: Optional[int]
    ) -> Dict[str, Any]:
        """Helper to create consistent payload structure"""
        payload = {"query": query, "texts": documents}
        if top_k is not None:
            payload["top_k"] = top_k
        return payload

    def _process_response(
        self, response_data: List[Dict[str, Any]], documents: List[str]
    ) -> List[Dict[str, Any]]:
        """Helper to process and normalize service response"""
        ranked_docs = [
            {
                "document": documents[result["index"]],
                "score": result["score"],
                "original_index": result["index"],
            }
            for result in response_data
        ]
        # Ensure results are sorted by score (descending)
        return sorted(ranked_docs, key=lambda x: x["score"], reverse=True)
