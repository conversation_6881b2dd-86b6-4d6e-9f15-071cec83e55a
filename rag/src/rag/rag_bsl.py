import threading
import async<PERSON>
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor
from functools import lru_cache
from fast_service import RequestContext, FastServiceConfig
from pydantic import BaseModel
from typing import List
from .settings import (
    LLMSettings,
    VDBSettings,
    SplitterSettings,
    EmbeddingSettings,
    RetrieverSettings,
    GenerationSettings,
)
from .settings import get_settings
from .shared import rag_fsm
from .shared import RAGE2EInput, RAGE2EOutput
from .shared import CollectionClearingInput
from .shared import IndexingInput, IndexingOutput
from .shared import DocLoadingInput, DocLoadingOutput
from .shared import DocSplittingInput, DocSplittingOutput
from .shared import EmbeddingDocsInput
from .shared import VDBStoringInput, VDBStoringOutput
from .shared import QueryTranslationInput
from .shared import PostRetrievalInput, PostRetrievalOutput
from .shared import GenerationInput
from .shared import RetrievalJudgingInput
from .shared import RetrievalInput
from .shared import EmbeddingQueryInput
from .shared import VDBSearchingInput
from .shared import RetrievalOutput
from .utils import get_logger
from .utils import close_vdb_client

from .loading import load_docs
from .chunking import split_docs
from .embedding import embed_documents
from .embedding import embed_query, batch_embed_query
from .vstore import vdb_store
from .vstore import vdb_search, batch_vdb_search
from .rag_indexing import clear_collection
from .retrieval import retrieval, batch_retrieval
from .pre_retrieval import query_translation
from .post_retrieval import post_retrieval
from .generation import generation
from .judging import retrieval_judger


class RAGServiceGlobalContext:
    def __init__(self):
        self.ctx = {}

    def has_context(self, context: RequestContext) -> bool:
        if context is None:
            return False
        return context.request_id in self.ctx

    def add_context(self, context: RequestContext):
        if self.has_context(context):
            get_logger().warning(f"Context {context.request_id} already exists")
            return
        self.ctx[context.request_id] = {
            "status": "Running",
            "branch": None,
            # "lock": threading.Lock(),
        }
        return self.ctx[context.request_id]

    def update_status(self, context: RequestContext, status: str = "Running"):
        assert self.has_context(context)
        # with self.ctx[context.request_id]["lock"]:
        self.ctx[context.request_id]["status"] = status

    def get_status(self, context: RequestContext) -> str:
        assert self.has_context(context)
        # with self.ctx[context.request_id]["lock"]:
        return self.ctx[context.request_id]["status"]

    def update_branch(self, context: RequestContext, branch: str = None):
        if self.has_context(context):
            self.ctx[context.request_id]["branch"] = branch

    def get_branch(self, context: RequestContext) -> str | None:
        if not self.has_context(context):
            return None
        return self.ctx[context.request_id]["branch"]


@lru_cache
def get_global_context():
    global_ctx = RAGServiceGlobalContext()
    return global_ctx


def extract_all_settings(item: RAGE2EInput):
    if item.splitter_settings is not None:
        splitter_settings = item.splitter_settings
    else:
        splitter_settings = SplitterSettings.from_default_settings()
    if item.vdb_settings is not None:
        vdb_settings = item.vdb_settings
    else:
        vdb_settings = VDBSettings.from_default_settings()
    if item.embedding_settings is not None:
        embedding_settings = item.embedding_settings
    else:
        embedding_settings = EmbeddingSettings.from_default_settings()
    if item.retriever_settings is not None:
        retriever_settings = item.retriever_settings
    else:
        retriever_settings = RetrieverSettings.from_default_settings()
    if item.generation_settings is not None:
        generation_settings = item.generation_settings
    else:
        generation_settings = GenerationSettings.from_default_settings()
    return (
        splitter_settings,
        vdb_settings,
        embedding_settings,
        retriever_settings,
        generation_settings,
    )


@rag_fsm.fast_service
def bsl_indexing(item: IndexingInput, context: RequestContext = None) -> IndexingOutput:
    docs: DocLoadingOutput = load_docs(
        item=DocLoadingInput(type=item.type, uri=item.uri), context=context
    )
    splits: DocSplittingOutput = split_docs(
        item=DocSplittingInput(
            docs=docs.docs,
            splitter_model=item.splitter_model,
            chunk_size=item.chunk_size,
            chunk_overlap=item.chunk_overlap,
        ),
        context=context,
    )

    embeddings = embed_documents(
        item=EmbeddingDocsInput(
            docs=splits.docs,
            embedding_model=item.embedding_model,
            embedding_device=item.embedding_device,
            embedding_batch_size=item.embedding_batch_size,
        ),
        context=context,
    ).embeddings

    ret: VDBStoringOutput = vdb_store(
        item=VDBStoringInput(
            vdb_type=item.vdb_type,
            vdb_uri=item.vdb_uri,
            collection_name=item.collection_name,
            docs=splits.docs,
            embeddings=embeddings,
        ),
        context=context,
    )
    get_logger().debug(f"Advanced RAG indexing output {context.request_id}: {ret}")

    return IndexingOutput.model_construct(**ret.model_dump())


@rag_fsm.fast_service
def bsl_retrieval(
    item: RetrievalInput, context: RequestContext = None
) -> RetrievalOutput:
    get_logger().debug(f"Retrieving documents for query: {item.query}")
    embedding = embed_query(
        item=EmbeddingQueryInput(
            query=item.query,
            embedding_model=item.embedding_model,
            embedding_device=item.embedding_device,
        ),
        context=context,
    ).embedding
    docs = vdb_search(
        item=VDBSearchingInput(
            vdb_type=item.vdb_type,
            vdb_uri=item.vdb_uri,
            collection_name=item.collection_name,
            query=item.query,
            embedding=embedding,
            k=item.search_args.get("k", 4) if item.search_args else 4,
        ),
        context=context,
    ).docs
    return RetrievalOutput(docs=docs)


@rag_fsm.fast_service
def bsl_naive_rag(item: RAGE2EInput, context: RequestContext = None) -> RAGE2EOutput:
    (
        splitter_settings,
        vdb_settings,
        embedding_settings,
        retriever_settings,
        generation_settings,
    ) = extract_all_settings(item=item)
    try:
        indexing_input = IndexingInput(
            uri=item.uri,
            type=item.type,
            splitter_model=splitter_settings.splitter_model,
            chunk_size=splitter_settings.chunk_size,
            chunk_overlap=splitter_settings.chunk_overlap,
            vdb_type=vdb_settings.vdb_type,
            vdb_uri=vdb_settings.vdb_uri,
            collection_name=vdb_settings.collection_name,
            embedding_model=embedding_settings.embedding_model,
            embedding_device=embedding_settings.embedding_device,
            embedding_batch_size=embedding_settings.embedding_batch_size,
        )
        indexing_out = bsl_indexing(item=indexing_input, context=context)

        retrieval_input = RetrievalInput(
            query=item.query,
            search_type=retriever_settings.search_type,
            search_args=retriever_settings.search_args,
            retriever_type=retriever_settings.retriever_type,
            vdb_type=vdb_settings.vdb_type,
            vdb_uri=vdb_settings.vdb_uri,
            collection_name=vdb_settings.collection_name,
            embedding_model=embedding_settings.embedding_model,
            embedding_device=embedding_settings.embedding_device,
            embedding_batch_size=embedding_settings.embedding_batch_size,
        )
        retrieval_output = bsl_retrieval(item=retrieval_input, context=context)

        post_retrieval_input = PostRetrievalInput(retrieved=retrieval_output)
        get_logger().debug(
            f"post_retrieval input {context.request_id}: {post_retrieval_input}"
        )
        post_retrieval_output: PostRetrievalOutput = post_retrieval(
            item=post_retrieval_input, context=context
        )
        get_logger().debug(
            f"post_retrieval output {context.request_id}: {post_retrieval_output}"
        )

        q_ctx = "\n\n".join([doc.page_content for doc in post_retrieval_output.docs])

        generation_input = GenerationInput(
            query=item.query,
            context=q_ctx,
            method="normal",
        )
        get_logger().debug(f"generation input {context.request_id}: {generation_input}")
        generation_output = generation(item=generation_input, context=context)
        get_logger().debug(
            f"generation output {context.request_id}: {generation_output}"
        )
    except Exception as e:
        get_logger().error(f"Error in bsl_advanced_rag: {e}")
        raise e
    finally:
        # Ensure the VDB client is closed after processing
        get_logger().debug(
            f"Closing VDB client {context.request_id}: {vdb_settings.vdb_uri}"
        )
        close_vdb_client(
            vdb_type=vdb_settings.vdb_type,
            vdb_uri=vdb_settings.vdb_uri,
            collection_name=vdb_settings.collection_name,
        )
    return RAGE2EOutput(answer=generation_output.answer)


class BatchRetrievalAndPostInput(BaseModel):
    original_query: str
    queries: List[str]
    qtypes: List[str]
    retriever_settings: RetrieverSettings
    vdb_settings: VDBSettings
    embedding_settings: EmbeddingSettings


class BatchRetrievalAndPostOutput(BaseModel):
    generation_context: str


@rag_fsm.fast_service
def bsl_batch_retrieval_and_post(
    item: BatchRetrievalAndPostInput, context: RequestContext = None
) -> BatchRetrievalAndPostOutput:
    retrieval_inputs = []
    for query, qtype in zip(item.queries, item.qtypes):
        retrieval_input = RetrievalInput(
            query=query,
            search_type=item.retriever_settings.search_type,
            search_args=item.retriever_settings.search_args,
            retriever_type=item.retriever_settings.retriever_type,
            vdb_type=item.vdb_settings.vdb_type,
            vdb_uri=item.vdb_settings.vdb_uri,
            collection_name=item.vdb_settings.collection_name,
            embedding_model=item.embedding_settings.embedding_model,
            embedding_device=item.embedding_settings.embedding_device,
            embedding_batch_size=item.embedding_settings.embedding_batch_size,
        )
        retrieval_inputs.append(retrieval_input)

    retrieval_outputs = batch_retrieval(items=retrieval_inputs, context=context)

    reranking_args = {
        "model": get_settings().reranker_model,
        "engine": get_settings().reranker_engine,
        "query": item.original_query,
    }

    post_retrieval_input = PostRetrievalInput(
        retrieved=retrieval_outputs,
        reranking_args=reranking_args,
    )
    get_logger().debug(
        f"post_retrieval input {context.request_id}: {post_retrieval_input}"
    )
    post_retrieval_output: PostRetrievalOutput = post_retrieval(
        item=post_retrieval_input, context=context
    )
    get_logger().debug(
        f"post_retrieval output {context.request_id}: {post_retrieval_output}"
    )

    if item.retriever_settings.search_args is not None:
        k = item.retriever_settings.search_args.get("k", 4)
    else:
        k = 4
    retrieved_docs = post_retrieval_output.docs[:k]
    gen_ctx = "\n\n".join([doc.page_content for doc in retrieved_docs])

    return BatchRetrievalAndPostOutput(generation_context=gen_ctx)


@rag_fsm.fast_service
def bsl_advanced_rag(item: RAGE2EInput, context: RequestContext = None) -> RAGE2EOutput:
    (
        splitter_settings,
        vdb_settings,
        embedding_settings,
        retriever_settings,
        generation_settings,
    ) = extract_all_settings(item=item)
    try:
        indexing_input = IndexingInput(
            uri=item.uri,
            type=item.type,
            splitter_model=splitter_settings.splitter_model,
            chunk_size=splitter_settings.chunk_size,
            chunk_overlap=splitter_settings.chunk_overlap,
            vdb_type=vdb_settings.vdb_type,
            vdb_uri=vdb_settings.vdb_uri,
            collection_name=vdb_settings.collection_name,
            embedding_model=embedding_settings.embedding_model,
            embedding_device=embedding_settings.embedding_device,
            embedding_batch_size=embedding_settings.embedding_batch_size,
        )
        indexing_out = bsl_indexing(item=indexing_input, context=context)

        qtrans_method = "MultiQuery"
        assert generation_settings.generation_method == "normal"
        qtrans_input = QueryTranslationInput(query=item.query, method=qtrans_method)
        get_logger().debug(
            f"query_translation input {context.request_id}: {qtrans_input}"
        )
        qtrans_output = query_translation(item=qtrans_input, context=context)
        get_logger().debug(
            f"query_translation output {context.request_id}: {qtrans_output}"
        )

        bsl_retrieval_out: BatchRetrievalAndPostOutput = bsl_batch_retrieval_and_post(
            item=BatchRetrievalAndPostInput(
                original_query=item.query,
                queries=qtrans_output.queries,
                qtypes=qtrans_output.qtypes,
                retriever_settings=retriever_settings,
                vdb_settings=vdb_settings,
                embedding_settings=embedding_settings,
            ),
            context=context,
        )
        gen_ctx = bsl_retrieval_out.generation_context

        generation_input = GenerationInput(
            query=item.query,
            context=gen_ctx,
            method=generation_settings.generation_method,
            method_args=generation_settings.generation_method_args,
        )
        get_logger().debug(f"generation input {context.request_id}: {generation_input}")
        generation_output = generation(item=generation_input, context=context)
        get_logger().debug(
            f"generation output {context.request_id}: {generation_output}"
        )

        if item.clear_collection:
            clearing_item = CollectionClearingInput(
                vdb_type=vdb_settings.vdb_type,
                vdb_uri=vdb_settings.vdb_uri,
                collection_name=vdb_settings.collection_name,
            )
            y3 = clear_collection(item=clearing_item, context=context)
            get_logger().debug(f"Naive RAG clearing output {context.request_id}: {y3}")
    except Exception as e:
        get_logger().error(f"Error in bsl_advanced_rag: {e}")
        raise e
    finally:
        # Ensure the VDB client is closed after processing
        get_logger().debug(
            f"Closing VDB client {context.request_id}: {vdb_settings.vdb_uri}"
        )
        close_vdb_client(
            vdb_type=vdb_settings.vdb_type,
            vdb_uri=vdb_settings.vdb_uri,
            collection_name=vdb_settings.collection_name,
        )
    return RAGE2EOutput(answer=generation_output.answer)


@rag_fsm.fast_service
def bsl_dynamic_rag(item: RAGE2EInput, context: RequestContext = None) -> RAGE2EOutput:
    retrieval_judger_input = RetrievalJudgingInput(question=item.query)
    retrieval_judger_output = retrieval_judger(
        item=retrieval_judger_input, context=context
    )
    need_retrieval = retrieval_judger_output.judgement
    if need_retrieval:
        return bsl_advanced_rag(item=item, context=context)
    else:
        get_logger().debug("Skip retrieval")
        generation_input = GenerationInput(
            query=item.query,
            context="",
            method="direct",
        )
        generation_output = generation(item=generation_input, context=context)
        return RAGE2EOutput(answer=generation_output.answer)


@rag_fsm.fast_service
def speculative_retrieval(
    item: RetrievalInput, context: RequestContext = None
) -> RetrievalOutput:
    get_logger().debug(f"Retrieving documents for query: {item.query}")

    current_branch = get_global_context().get_branch(context=context)
    if current_branch and current_branch != "advanced":
        return RetrievalOutput(docs=[])

    embedding = embed_query(
        item=EmbeddingQueryInput(
            query=item.query,
            embedding_model=item.embedding_model,
            embedding_device=item.embedding_device,
        ),
        context=context,
    ).embedding

    current_branch = get_global_context().get_branch(context=context)
    if current_branch and current_branch != "advanced":
        return RetrievalOutput(docs=[])

    docs = vdb_search(
        item=VDBSearchingInput(
            vdb_type=item.vdb_type,
            vdb_uri=item.vdb_uri,
            collection_name=item.collection_name,
            query=item.query,
            embedding=embedding,
            k=item.search_args.get("k", 4) if item.search_args else 4,
        ),
        context=context,
    ).docs
    return RetrievalOutput(docs=docs)


@rag_fsm.fast_service
def speculative_batch_retrieval(
    items: list[RetrievalInput], context: RequestContext = None
) -> list[RetrievalOutput]:
    for item in items:
        get_logger().debug(f"retrieval input {context.request_id}: {item}")
    ret = []
    for item in items:
        current_branch = get_global_context().get_branch(context=context)
        if current_branch and current_branch != "advanced":
            return ret
        retrieval_output = speculative_retrieval(item=item, context=context)
        ret.append(retrieval_output)
    for rout in ret:
        get_logger().debug(f"retrieval output {context.request_id}: {rout}")
    return ret


@rag_fsm.fast_service
def speculative_advanced_rag(
    item: RAGE2EInput, context: RequestContext = None
) -> RAGE2EOutput:
    (
        splitter_settings,
        vdb_settings,
        embedding_settings,
        retriever_settings,
        generation_settings,
    ) = extract_all_settings(item=item)

    def clear_and_close():
        if item.clear_collection:
            clearing_item = CollectionClearingInput(
                vdb_type=vdb_settings.vdb_type,
                vdb_uri=vdb_settings.vdb_uri,
                collection_name=vdb_settings.collection_name,
            )
            y3 = clear_collection(item=clearing_item, context=context)
            get_logger().debug(f"Naive RAG clearing output {context.request_id}: {y3}")
        # Ensure the VDB client is closed after processing
        get_logger().debug(
            f"Closing VDB client {context.request_id}: {vdb_settings.vdb_uri}"
        )
        close_vdb_client(
            vdb_type=vdb_settings.vdb_type,
            vdb_uri=vdb_settings.vdb_uri,
            collection_name=vdb_settings.collection_name,
        )

    try:
        docs: DocLoadingOutput = load_docs(
            item=DocLoadingInput(type=item.type, uri=item.uri), context=context
        )

        current_branch = get_global_context().get_branch(context=context)
        if current_branch and current_branch != "advanced":
            return None

        splits: DocSplittingOutput = split_docs(
            item=DocSplittingInput(
                docs=docs.docs,
                splitter_model=splitter_settings.splitter_model,
                chunk_size=splitter_settings.chunk_size,
                chunk_overlap=splitter_settings.chunk_overlap,
            ),
            context=context,
        )

        current_branch = get_global_context().get_branch(context=context)
        if current_branch and current_branch != "advanced":
            return None

        embeddings = embed_documents(
            item=EmbeddingDocsInput(
                docs=splits.docs,
                embedding_model=embedding_settings.embedding_model,
                embedding_device=embedding_settings.embedding_device,
                embedding_batch_size=embedding_settings.embedding_batch_size,
            ),
            context=context,
        ).embeddings

        current_branch = get_global_context().get_branch(context=context)
        if current_branch and current_branch != "advanced":
            return None

        ret: VDBStoringOutput = vdb_store(
            item=VDBStoringInput(
                vdb_type=vdb_settings.vdb_type,
                vdb_uri=vdb_settings.vdb_uri,
                collection_name=vdb_settings.collection_name,
                docs=splits.docs,
                embeddings=embeddings,
            ),
            context=context,
        )
        get_logger().debug(f"Advanced RAG indexing output {context.request_id}: {ret}")

        current_branch = get_global_context().get_branch(context=context)
        if current_branch and current_branch != "advanced":
            clear_and_close()
            return None

        qtrans_method = "MultiQuery"
        reranking_args = {
            "model": get_settings().reranker_model,
            "engine": get_settings().reranker_engine,
            "query": item.query,
        }
        assert generation_settings.generation_method == "normal"

        qtrans_input = QueryTranslationInput(query=item.query, method=qtrans_method)
        get_logger().debug(
            f"query_translation input {context.request_id}: {qtrans_input}"
        )
        qtrans_output = query_translation(item=qtrans_input, context=context)
        get_logger().debug(
            f"query_translation output {context.request_id}: {qtrans_output}"
        )

        current_branch = get_global_context().get_branch(context=context)
        if current_branch and current_branch != "advanced":
            clear_and_close()
            return None

        queries = qtrans_output.queries
        qtypes = qtrans_output.qtypes

        retrieval_inputs = []
        for query, qtype in zip(queries, qtypes):
            retrieval_input = RetrievalInput(
                query=query,
                search_type=retriever_settings.search_type,
                search_args=retriever_settings.search_args,
                retriever_type=retriever_settings.retriever_type,
                vdb_type=vdb_settings.vdb_type,
                vdb_uri=vdb_settings.vdb_uri,
                collection_name=vdb_settings.collection_name,
                embedding_model=embedding_settings.embedding_model,
                embedding_device=embedding_settings.embedding_device,
                embedding_batch_size=embedding_settings.embedding_batch_size,
            )
            retrieval_inputs.append(retrieval_input)
        retrieval_outputs = speculative_batch_retrieval(
            items=retrieval_inputs, context=context
        )

        current_branch = get_global_context().get_branch(context=context)
        if current_branch and current_branch != "advanced":
            clear_and_close()
            return None

        post_retrieval_input = PostRetrievalInput(
            retrieved=retrieval_outputs,
            reranking_args=reranking_args,
        )
        get_logger().debug(
            f"post_retrieval input {context.request_id}: {post_retrieval_input}"
        )
        post_retrieval_output: PostRetrievalOutput = post_retrieval(
            item=post_retrieval_input, context=context
        )
        get_logger().debug(
            f"post_retrieval output {context.request_id}: {post_retrieval_output}"
        )

        current_branch = get_global_context().get_branch(context=context)
        if current_branch and current_branch != "advanced":
            clear_and_close()
            return None

        if retriever_settings.search_args is not None:
            k = retriever_settings.search_args.get("k", 4)
        else:
            k = 4
        retrieved_docs = post_retrieval_output.docs[:k]

        q_ctx = "\n\n".join([doc.page_content for doc in retrieved_docs])
        generation_input = GenerationInput(
            query=item.query,
            context=q_ctx,
            method=generation_settings.generation_method,
            method_args=generation_settings.generation_method_args,
        )
        get_logger().debug(f"generation input {context.request_id}: {generation_input}")
        generation_output = generation(item=generation_input, context=context)
        get_logger().debug(
            f"generation output {context.request_id}: {generation_output}"
        )

        if item.clear_collection:
            clearing_item = CollectionClearingInput(
                vdb_type=vdb_settings.vdb_type,
                vdb_uri=vdb_settings.vdb_uri,
                collection_name=vdb_settings.collection_name,
            )
            y3 = clear_collection(item=clearing_item, context=context)
            get_logger().debug(f"Naive RAG clearing output {context.request_id}: {y3}")
    except Exception as e:
        get_logger().error(f"Error in bsl_advanced_rag: {e}")
        raise e
    finally:
        # Ensure the VDB client is closed after processing
        get_logger().debug(
            f"Closing VDB client {context.request_id}: {vdb_settings.vdb_uri}"
        )
        close_vdb_client(
            vdb_type=vdb_settings.vdb_type,
            vdb_uri=vdb_settings.vdb_uri,
            collection_name=vdb_settings.collection_name,
        )
    return RAGE2EOutput(answer=generation_output.answer)


@rag_fsm.fast_service
def speculative_dynamic_rag(
    item: RAGE2EInput, context: RequestContext = None
) -> RAGE2EOutput:
    get_global_context().add_context(context)

    def worker_0() -> bool:
        retrieval_judger_input = RetrievalJudgingInput(question=item.query)
        retrieval_judger_output = retrieval_judger(
            item=retrieval_judger_input, context=context
        )
        need_retrieval = retrieval_judger_output.judgement
        get_global_context().update_branch(
            context, branch="advanced" if need_retrieval else "direct"
        )
        return need_retrieval

    def worker_1() -> RAGE2EOutput | None:
        return speculative_advanced_rag(item=item, context=context)

    def worker_2() -> RAGE2EOutput | None:
        get_logger().debug("Skip retrieval")
        generation_input = GenerationInput(
            query=item.query,
            context="",
            method="direct",
        )
        generation_output = generation(item=generation_input, context=context)
        return RAGE2EOutput(answer=generation_output.answer)

    # run 3 workers in parallel in threadpool, each with a thread
    with ThreadPoolExecutor(max_workers=3) as executor:
        futures = [
            executor.submit(worker_0),
            executor.submit(worker_1),
            executor.submit(worker_2),
        ]
    ret = [future.result() for future in futures]
    need_retrieval: bool = ret[0]
    if need_retrieval:
        return ret[1]
    else:
        return ret[2]


@rag_fsm.fast_service
def selective_speculative_dynamic_rag(
    item: RAGE2EInput, context: RequestContext = None
) -> RAGE2EOutput:
    get_global_context().add_context(context)

    def worker_1() -> RAGE2EOutput | bool:
        retrieval_judger_input = RetrievalJudgingInput(question=item.query)
        retrieval_judger_output = retrieval_judger(
            item=retrieval_judger_input, context=context
        )
        need_retrieval = retrieval_judger_output.judgement
        get_global_context().update_branch(
            context, branch="advanced" if need_retrieval else "direct"
        )
        if need_retrieval:
            return speculative_advanced_rag(item=item, context=context)
        else:
            return need_retrieval

    def worker_2() -> RAGE2EOutput | None:
        get_logger().debug("Skip retrieval")
        generation_input = GenerationInput(
            query=item.query,
            context="",
            method="direct",
        )
        generation_output = generation(item=generation_input, context=context)
        return RAGE2EOutput(answer=generation_output.answer)

    # run 3 workers in parallel in threadpool, each with a thread
    with ThreadPoolExecutor(max_workers=3) as executor:
        futures = [
            executor.submit(worker_1),
            executor.submit(worker_2),
        ]
    ret = [future.result() for future in futures]
    if isinstance(ret[0], bool) and ret[0] is False:
        get_logger().debug("No retrieval needed, returning direct generation")
        return ret[1]
    else:
        return ret[0]


from .async_rag_indexing import (
    indexing_async,
    contextual_indexing_async,
    clear_collection_async,
)
from .async_rag_chatting import (
    naive_rag_chatting_async,
    multiquery_rag_chatting_async,
    dynamic_rag_chatting_async,
)
from .async_loading import load_docs_async
from .async_chunking import split_docs_async
from .async_embedding import (
    embed_documents_async,
    embed_query_async,
    batch_embed_query_async,
    EmbeddingQueryOutput,
)
from .async_vstore import (
    vdb_store_async,
    get_async_vdb_client,
    vdb_search_async,
    batch_vdb_search_async,
)
from .async_chunk_situating import situate_chunks_async
from .async_pre_retrieval import query_translation_async
from .async_retrieval import retrieval_async, batch_retrieval_async
from .async_post_retrieval import post_retrieval_async
from .async_generation import generation_async
from .async_judging import retrieval_judger_async
from .async_utils import get_logger, get_settings, close_async_vdb_client
from .async_utils import get_logger, async_gather_with_concurrency


@rag_fsm.async_fast_service
async def bsl_indexing_async(
    item: IndexingInput, context: RequestContext = None
) -> IndexingOutput:
    docs: DocLoadingOutput = await load_docs_async(
        item=DocLoadingInput(type=item.type, uri=item.uri), context=context
    )
    splits: DocSplittingOutput = await split_docs_async(
        item=DocSplittingInput(
            docs=docs.docs,
            splitter_model=item.splitter_model,
            chunk_size=item.chunk_size,
            chunk_overlap=item.chunk_overlap,
        ),
        context=context,
    )

    embed_out = await embed_documents_async(
        item=EmbeddingDocsInput(
            docs=splits.docs,
            embedding_model=item.embedding_model,
            embedding_device=item.embedding_device,
            embedding_batch_size=item.embedding_batch_size,
        ),
        context=context,
    )
    embeddings = embed_out.embeddings

    ret: VDBStoringOutput = await vdb_store_async(
        item=VDBStoringInput(
            vdb_type=item.vdb_type,
            vdb_uri=item.vdb_uri,
            collection_name=item.collection_name,
            docs=splits.docs,
            embeddings=embeddings,
        ),
        context=context,
    )
    get_logger().debug(f"Advanced RAG indexing output {context.request_id}: {ret}")

    return IndexingOutput.model_construct(**ret.model_dump())


@rag_fsm.async_fast_service
async def bsl_naive_rag_async(
    item: RAGE2EInput, context: RequestContext = None
) -> RAGE2EOutput:
    (
        splitter_settings,
        vdb_settings,
        embedding_settings,
        retriever_settings,
        generation_settings,
    ) = extract_all_settings(item=item)
    try:
        indexing_input = IndexingInput(
            uri=item.uri,
            type=item.type,
            splitter_model=splitter_settings.splitter_model,
            chunk_size=splitter_settings.chunk_size,
            chunk_overlap=splitter_settings.chunk_overlap,
            vdb_type=vdb_settings.vdb_type,
            vdb_uri=vdb_settings.vdb_uri,
            collection_name=vdb_settings.collection_name,
            embedding_model=embedding_settings.embedding_model,
            embedding_device=embedding_settings.embedding_device,
            embedding_batch_size=embedding_settings.embedding_batch_size,
        )
        embedding_query_input = EmbeddingQueryInput(
            query=item.query,
            embedding_model=embedding_settings.embedding_model,
            embedding_device=embedding_settings.embedding_device,
        )
        tasks = [
            bsl_indexing_async(item=indexing_input, context=context),
            embed_query_async(
                item=embedding_query_input,
                context=context,
            ),
        ]
        indexing_out, embedding_output = await asyncio.gather(*tasks)

        # Search vector database
        search_output = await vdb_search_async(
            item=VDBSearchingInput(
                vdb_type=vdb_settings.vdb_type,
                vdb_uri=vdb_settings.vdb_uri,
                collection_name=vdb_settings.collection_name,
                query=item.query,
                embedding=embedding_output.embedding,
                k=(
                    retriever_settings.search_args.get("k", 4)
                    if retriever_settings.search_args
                    else 4
                ),
            ),
            context=context,
        )

        post_retrieval_input = PostRetrievalInput(
            retrieved=RetrievalOutput(docs=search_output.docs)
        )
        get_logger().debug(
            f"post_retrieval input {context.request_id}: {post_retrieval_input}"
        )
        post_retrieval_output: PostRetrievalOutput = await post_retrieval_async(
            item=post_retrieval_input, context=context
        )
        get_logger().debug(
            f"post_retrieval output {context.request_id}: {post_retrieval_output}"
        )

        q_ctx = "\n\n".join([doc.page_content for doc in post_retrieval_output.docs])

        generation_input = GenerationInput(
            query=item.query,
            context=q_ctx,
            method="normal",
        )
        get_logger().debug(f"generation input {context.request_id}: {generation_input}")
        generation_output = await generation_async(
            item=generation_input, context=context
        )
        get_logger().debug(
            f"generation output {context.request_id}: {generation_output}"
        )
    except Exception as e:
        get_logger().error(f"Error in bsl_advanced_rag: {e}")
        raise e
    finally:
        # Ensure the VDB client is closed after processing
        get_logger().debug(
            f"Closing VDB client {context.request_id}: {vdb_settings.vdb_uri}"
        )
        await close_async_vdb_client(
            vdb_type=vdb_settings.vdb_type,
            vdb_uri=vdb_settings.vdb_uri,
            collection_name=vdb_settings.collection_name,
        )
    return RAGE2EOutput(answer=generation_output.answer)


@rag_fsm.async_fast_service
async def bsl_batch_retrieval_async(
    items: list[RetrievalInput], context: RequestContext = None
) -> list[RetrievalOutput]:
    """Retrieve documents for multiple queries asynchronously."""
    if not items:
        return []

    for item in items:
        get_logger().debug(f"retrieval input {context.request_id}: {item}")

    # Parallel individual retrievals
    tasks = [retrieval_async(item=item, context=context) for item in items]
    ret = await asyncio.gather(*tasks)
    for rout in ret:
        get_logger().debug(f"retrieval output {context.request_id}: {rout}")

    return ret


@rag_fsm.async_fast_service
async def bsl_advanced_rag_async(
    item: RAGE2EInput, context: RequestContext = None
) -> RAGE2EOutput:
    (
        splitter_settings,
        vdb_settings,
        embedding_settings,
        retriever_settings,
        generation_settings,
    ) = extract_all_settings(item=item)
    try:

        indexing_input = IndexingInput(
            uri=item.uri,
            type=item.type,
            splitter_model=splitter_settings.splitter_model,
            chunk_size=splitter_settings.chunk_size,
            chunk_overlap=splitter_settings.chunk_overlap,
            vdb_type=vdb_settings.vdb_type,
            vdb_uri=vdb_settings.vdb_uri,
            collection_name=vdb_settings.collection_name,
            embedding_model=embedding_settings.embedding_model,
            embedding_device=embedding_settings.embedding_device,
            embedding_batch_size=embedding_settings.embedding_batch_size,
        )

        async def qtrans_and_embed() -> tuple[list[str], list[EmbeddingQueryOutput]]:
            qtrans_method = "MultiQuery"
            assert generation_settings.generation_method == "normal"

            qtrans_input = QueryTranslationInput(query=item.query, method=qtrans_method)
            get_logger().debug(
                f"query_translation input {context.request_id}: {qtrans_input}"
            )
            qtrans_output = await query_translation_async(
                item=qtrans_input, context=context
            )
            get_logger().debug(
                f"query_translation output {context.request_id}: {qtrans_output}"
            )
            queries = qtrans_output.queries
            qtypes = qtrans_output.qtypes

            embedding_outputs = await batch_embed_query_async(
                items=[
                    EmbeddingQueryInput(
                        query=qry,
                        embedding_model=embedding_settings.embedding_model,
                        embedding_device=embedding_settings.embedding_device,
                    )
                    for qry in queries
                ],
                context=context,
            )

            return queries, embedding_outputs

        tasks = [
            bsl_indexing_async(item=indexing_input, context=context),
            qtrans_and_embed(),
        ]
        indexing_out, qtrans_and_embed_out = await asyncio.gather(*tasks)
        queries, embedding_outputs = qtrans_and_embed_out

        search_outputs = await batch_vdb_search_async(
            items=[
                VDBSearchingInput(
                    vdb_type=vdb_settings.vdb_type,
                    vdb_uri=vdb_settings.vdb_uri,
                    collection_name=vdb_settings.collection_name,
                    query=qry,
                    embedding=embedding_output.embedding,
                    k=(
                        retriever_settings.search_args.get("k", 4)
                        if retriever_settings.search_args
                        else 4
                    ),
                )
                for qry, embedding_output in zip(queries, embedding_outputs)
            ],
            context=context,
        )
        retrieval_outputs = [
            RetrievalOutput(docs=output.docs) for output in search_outputs
        ]

        reranking_args = {
            "model": get_settings().reranker_model,
            "engine": get_settings().reranker_engine,
            "query": item.query,
        }

        post_retrieval_input = PostRetrievalInput(
            retrieved=retrieval_outputs,
            reranking_args=reranking_args,
        )
        get_logger().debug(
            f"post_retrieval input {context.request_id}: {post_retrieval_input}"
        )
        post_retrieval_output: PostRetrievalOutput = await post_retrieval_async(
            item=post_retrieval_input, context=context
        )
        get_logger().debug(
            f"post_retrieval output {context.request_id}: {post_retrieval_output}"
        )

        if retriever_settings.search_args is not None:
            k = retriever_settings.search_args.get("k", 4)
        else:
            k = 4
        retrieved_docs = post_retrieval_output.docs[:k]

        q_ctx = "\n\n".join([doc.page_content for doc in retrieved_docs])
        generation_input = GenerationInput(
            query=item.query,
            context=q_ctx,
            method=generation_settings.generation_method,
            method_args=generation_settings.generation_method_args,
        )
        get_logger().debug(f"generation input {context.request_id}: {generation_input}")
        generation_output = await generation_async(
            item=generation_input, context=context
        )
        get_logger().debug(
            f"generation output {context.request_id}: {generation_output}"
        )

        if item.clear_collection:
            clearing_item = CollectionClearingInput(
                vdb_type=vdb_settings.vdb_type,
                vdb_uri=vdb_settings.vdb_uri,
                collection_name=vdb_settings.collection_name,
            )
            y3 = await clear_collection_async(item=clearing_item, context=context)
            get_logger().debug(f"Naive RAG clearing output {context.request_id}: {y3}")
    except Exception as e:
        get_logger().error(f"Error in bsl_advanced_rag: {e}")
        raise e
    finally:
        # Ensure the VDB client is closed after processing
        get_logger().debug(
            f"Closing VDB client {context.request_id}: {vdb_settings.vdb_uri}"
        )
        await close_async_vdb_client(
            vdb_type=vdb_settings.vdb_type,
            vdb_uri=vdb_settings.vdb_uri,
            collection_name=vdb_settings.collection_name,
        )
    return RAGE2EOutput(answer=generation_output.answer)


@rag_fsm.async_fast_service
async def bsl_dynamic_rag_async(
    item: RAGE2EInput, context: RequestContext = None
) -> RAGE2EOutput:
    retrieval_judger_input = RetrievalJudgingInput(question=item.query)
    retrieval_judger_output = await retrieval_judger_async(
        item=retrieval_judger_input, context=context
    )
    need_retrieval = retrieval_judger_output.judgement
    if need_retrieval:
        return await bsl_advanced_rag_async(item=item, context=context)
    else:
        get_logger().debug("Skip retrieval")
        generation_input = GenerationInput(
            query=item.query,
            context="",
            method="direct",
        )
        generation_output = await generation_async(
            item=generation_input, context=context
        )
        return RAGE2EOutput(answer=generation_output.answer)


@rag_fsm.async_fast_service
async def speculative_retrieval_async(
    item: RetrievalInput, context: RequestContext = None
) -> RetrievalOutput:
    """Retrieve documents asynchronously."""
    get_logger().debug(f"Retrieving documents for query: {item.query}")

    current_branch = get_global_context().get_branch(context=context)
    if current_branch and current_branch != "advanced":
        return RetrievalOutput(docs=[])

    # Embed the query
    embedding_output = await embed_query_async(
        item=EmbeddingQueryInput(
            query=item.query,
            embedding_model=item.embedding_model,
            embedding_device=item.embedding_device,
        ),
        context=context,
    )

    current_branch = get_global_context().get_branch(context=context)
    if current_branch and current_branch != "advanced":
        return RetrievalOutput(docs=[])

    # Search vector database
    search_output = await vdb_search_async(
        item=VDBSearchingInput(
            vdb_type=item.vdb_type,
            vdb_uri=item.vdb_uri,
            collection_name=item.collection_name,
            query=item.query,
            embedding=embedding_output.embedding,
            k=item.search_args.get("k", 4) if item.search_args else 4,
        ),
        context=context,
    )

    return RetrievalOutput(docs=search_output.docs)


@rag_fsm.async_fast_service
async def speculative_batch_retrieval_async(
    items: list[RetrievalInput], context: RequestContext = None
) -> list[RetrievalOutput]:
    """Retrieve documents for multiple queries asynchronously."""
    if not items:
        return []
    for item in items:
        get_logger().debug(f"retrieval input {context.request_id}: {item}")
        tasks = [
            speculative_retrieval_async(item=item, context=context) for item in items
        ]
    ret = await asyncio.gather(*tasks, return_exceptions=True)
    for rout in ret:
        get_logger().debug(f"retrieval output {context.request_id}: {rout}")

    return ret


@rag_fsm.async_fast_service
async def speculative_advanced_rag_async(
    item: RAGE2EInput, context: RequestContext = None
) -> RAGE2EOutput:
    (
        splitter_settings,
        vdb_settings,
        embedding_settings,
        retriever_settings,
        generation_settings,
    ) = extract_all_settings(item=item)

    async def clear_and_close():
        if item.clear_collection:
            clearing_item = CollectionClearingInput(
                vdb_type=vdb_settings.vdb_type,
                vdb_uri=vdb_settings.vdb_uri,
                collection_name=vdb_settings.collection_name,
            )
            y3 = await clear_collection_async(item=clearing_item, context=context)
            get_logger().debug(f"Naive RAG clearing output {context.request_id}: {y3}")
        # Ensure the VDB client is closed after processing
        get_logger().debug(
            f"Closing VDB client {context.request_id}: {vdb_settings.vdb_uri}"
        )
        await close_async_vdb_client(
            vdb_type=vdb_settings.vdb_type,
            vdb_uri=vdb_settings.vdb_uri,
            collection_name=vdb_settings.collection_name,
        )

    try:
        docs: DocLoadingOutput = await load_docs_async(
            item=DocLoadingInput(type=item.type, uri=item.uri), context=context
        )

        current_branch = get_global_context().get_branch(context=context)
        if current_branch and current_branch != "advanced":
            return None

        splits: DocSplittingOutput = await split_docs_async(
            item=DocSplittingInput(
                docs=docs.docs,
                splitter_model=splitter_settings.splitter_model,
                chunk_size=splitter_settings.chunk_size,
                chunk_overlap=splitter_settings.chunk_overlap,
            ),
            context=context,
        )

        current_branch = get_global_context().get_branch(context=context)
        if current_branch and current_branch != "advanced":
            return None

        embed_out = await embed_documents_async(
            item=EmbeddingDocsInput(
                docs=splits.docs,
                embedding_model=embedding_settings.embedding_model,
                embedding_device=embedding_settings.embedding_device,
                embedding_batch_size=embedding_settings.embedding_batch_size,
            ),
            context=context,
        )
        embeddings = embed_out.embeddings

        current_branch = get_global_context().get_branch(context=context)
        if current_branch and current_branch != "advanced":
            return None

        ret: VDBStoringOutput = await vdb_store_async(
            item=VDBStoringInput(
                vdb_type=vdb_settings.vdb_type,
                vdb_uri=vdb_settings.vdb_uri,
                collection_name=vdb_settings.collection_name,
                docs=splits.docs,
                embeddings=embeddings,
            ),
            context=context,
        )
        get_logger().debug(f"Advanced RAG indexing output {context.request_id}: {ret}")

        current_branch = get_global_context().get_branch(context=context)
        if current_branch and current_branch != "advanced":
            await clear_and_close()
            return None

        qtrans_method = "MultiQuery"
        reranking_args = {
            "model": get_settings().reranker_model,
            "engine": get_settings().reranker_engine,
            "query": item.query,
        }
        assert generation_settings.generation_method == "normal"

        qtrans_input = QueryTranslationInput(query=item.query, method=qtrans_method)
        get_logger().debug(
            f"query_translation input {context.request_id}: {qtrans_input}"
        )
        qtrans_output = await query_translation_async(
            item=qtrans_input, context=context
        )
        get_logger().debug(
            f"query_translation output {context.request_id}: {qtrans_output}"
        )

        current_branch = get_global_context().get_branch(context=context)
        if current_branch and current_branch != "advanced":
            await clear_and_close()
            return None

        queries = qtrans_output.queries
        qtypes = qtrans_output.qtypes

        retrieval_inputs = []
        for query, qtype in zip(queries, qtypes):
            retrieval_input = RetrievalInput(
                query=query,
                search_type=retriever_settings.search_type,
                search_args=retriever_settings.search_args,
                retriever_type=retriever_settings.retriever_type,
                vdb_type=vdb_settings.vdb_type,
                vdb_uri=vdb_settings.vdb_uri,
                collection_name=vdb_settings.collection_name,
                embedding_model=embedding_settings.embedding_model,
                embedding_device=embedding_settings.embedding_device,
                embedding_batch_size=embedding_settings.embedding_batch_size,
            )
            retrieval_inputs.append(retrieval_input)
        retrieval_outputs = await speculative_batch_retrieval_async(
            items=retrieval_inputs, context=context
        )

        current_branch = get_global_context().get_branch(context=context)
        if current_branch and current_branch != "advanced":
            await clear_and_close()
            return None

        post_retrieval_input = PostRetrievalInput(
            retrieved=retrieval_outputs,
            reranking_args=reranking_args,
        )
        get_logger().debug(
            f"post_retrieval input {context.request_id}: {post_retrieval_input}"
        )
        post_retrieval_output: PostRetrievalOutput = await post_retrieval_async(
            item=post_retrieval_input, context=context
        )
        get_logger().debug(
            f"post_retrieval output {context.request_id}: {post_retrieval_output}"
        )
        current_branch = get_global_context().get_branch(context=context)
        if current_branch and current_branch != "advanced":
            await clear_and_close()
            return None

        if retriever_settings.search_args is not None:
            k = retriever_settings.search_args.get("k", 4)
        else:
            k = 4
        retrieved_docs = post_retrieval_output.docs[:k]

        q_ctx = "\n\n".join([doc.page_content for doc in retrieved_docs])
        generation_input = GenerationInput(
            query=item.query,
            context=q_ctx,
            method=generation_settings.generation_method,
            method_args=generation_settings.generation_method_args,
        )
        get_logger().debug(f"generation input {context.request_id}: {generation_input}")
        generation_output = await generation_async(
            item=generation_input, context=context
        )
        get_logger().debug(
            f"generation output {context.request_id}: {generation_output}"
        )

        await clear_and_close()
    except Exception as e:
        get_logger().error(f"Error in bsl_advanced_rag: {e}")
        await clear_and_close()
        raise e
    return RAGE2EOutput(answer=generation_output.answer)


@rag_fsm.async_fast_service
async def speculative_dynamic_rag_async(
    item: RAGE2EInput, context: RequestContext = None
) -> RAGE2EOutput:
    get_global_context().add_context(context)

    async def worker_0() -> bool:
        retrieval_judger_input = RetrievalJudgingInput(question=item.query)
        retrieval_judger_output = await retrieval_judger_async(
            item=retrieval_judger_input, context=context
        )
        need_retrieval = retrieval_judger_output.judgement
        get_global_context().update_branch(
            context, branch="advanced" if need_retrieval else "direct"
        )
        return need_retrieval

    async def worker_1() -> RAGE2EOutput | None:
        return await speculative_advanced_rag_async(item=item, context=context)

    async def worker_2() -> RAGE2EOutput | None:
        get_logger().debug("Skip retrieval")
        generation_input = GenerationInput(
            query=item.query,
            context="",
            method="direct",
        )
        current_branch = get_global_context().get_branch(context=context)
        if current_branch and current_branch != "direct":
            return None
        generation_output = await generation_async(
            item=generation_input, context=context
        )
        return RAGE2EOutput(answer=generation_output.answer)

    # run 3 workers in parallel with async gather
    ret = await asyncio.gather(
        worker_0(), worker_1(), worker_2(), return_exceptions=True
    )
    need_retrieval: bool = ret[0]
    if need_retrieval:
        return ret[1]
    else:
        return ret[2]


@rag_fsm.async_fast_service
async def selective_speculative_dynamic_rag_async(
    item: RAGE2EInput, context: RequestContext = None
) -> RAGE2EOutput:
    get_global_context().add_context(context)

    async def worker_1() -> RAGE2EOutput | bool:
        retrieval_judger_input = RetrievalJudgingInput(question=item.query)
        retrieval_judger_output = await retrieval_judger_async(
            item=retrieval_judger_input, context=context
        )
        need_retrieval = retrieval_judger_output.judgement
        get_global_context().update_branch(
            context, branch="advanced" if need_retrieval else "direct"
        )
        if need_retrieval:
            return await speculative_advanced_rag_async(item=item, context=context)
        else:
            return need_retrieval

    async def worker_2() -> RAGE2EOutput | None:
        get_logger().debug("Skip retrieval")
        generation_input = GenerationInput(
            query=item.query,
            context="",
            method="direct",
        )
        current_branch = get_global_context().get_branch(context=context)
        if current_branch and current_branch != "direct":
            return None
        generation_output = await generation_async(
            item=generation_input, context=context
        )
        return RAGE2EOutput(answer=generation_output.answer)

    # run 2 workers in parallel with async gather
    # ret = await asyncio.gather(worker_1(), worker_2(), return_exceptions=True)
    # submit the workers to the event loop
    task1 = asyncio.create_task(worker_1())
    task2 = asyncio.create_task(worker_2())

    need_retrieval_or_ret = await task1

    if isinstance(need_retrieval_or_ret, bool) and need_retrieval_or_ret is False:
        get_logger().debug("No retrieval needed, returning direct generation")
        return await task2
    else:
        return need_retrieval_or_ret
