import os,sys
import pandas as pd
import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Optional

e2e_task_name = "bsl_dynamic_rag"
task_names = [
    "load_docs",
    "split_docs",
    "situate_chunks",
    "embed_documents",
    "vdb_store",
    "retrieval_judger",
    "query_translation",
    "embed_query",
    "vdb_search",
    "post_retrieval",
    "generation",
    "batch_embed_query",
    "batch_vdb_search",
    "normal_generation",
    "direct_generation",
]


dpaths = {
    "component-dist.": "/mnt/data/agbench/results/rag/dynamic_rag/opt/baseline-temp07/all-replicated/bsl_dynamic-WQA-FixedInterval-12.0-10000/results/server/monitor/rag_server/clean-stats/e2e_delay.csv",
    "task-dist.": "/mnt/data/agbench/results/rag/dynamic_rag/opt/baseline-temp07/task-llm-task-embed/bsl_dynamic-WQA-FixedInterval-12.0-10000/results/server/monitor/rag_server/clean-stats/e2e_delay.csv",
}

save_home = "./.cache/task-dist"
os.makedirs(save_home, exist_ok=True)

dfs = []
for exp, dpath in dpaths.items():
    df = pd.read_csv(dpath)
    df['service_name'] = df['service_name'].apply(lambda x: x.split(".")[-1])
    df = df[['service_name', 'avg_delay', 'total_records']]
    df['total_seconds'] = df['avg_delay'] * df['total_records']
    df["experiment"] = exp
    dfs.append(df)

final_df = pd.concat(dfs, ignore_index=True)
#save final_df to savehome/data
final_df.to_csv(os.path.join(save_home, "data.csv"), index=False)

def plot_comparision_with_breakdown(
    df: pd.DataFrame,
    title: Optional[str] = None,
    xlabel: Optional[str] = None,
    ylabel: Optional[str] = None,
    save_dir: Optional[str] = None
) -> None:
    """Plot comparison with breakdown by experiment.
    There are two rows with same length, one row for one experiment.
    Each row, there are stacked bars, each bar indicates the percentage of latency contributed by a service
    Alongside each row, show the avg_delay of {e2e_task_name} in that experiment.
    """
    # TODO
    # save plot
    if save_dir:
        plt.savefig(os.path.join(save_dir, f"{title}.png"))
    plt.show()


plot_comparision_with_breakdown(final_df, title="Average Delay Comparison", save_dir=save_home)