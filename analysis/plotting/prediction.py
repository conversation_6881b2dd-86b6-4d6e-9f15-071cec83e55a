import pandas as pd
import os, sys
import pandas as pd
from pandas import DataFrame
from typing import Optional, Dict
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from tqdm import tqdm
from difflib import SequenceMatcher
from typing import List, Tuple

module_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
print(f"Adding module path: {module_path}")
if module_path not in sys.path:
    print(f"Appending {module_path} to sys.path")
    sys.path.append(module_path)

from llm.io_data import parse_llm_call_metrics
from plotting.utils import shorten_task_name