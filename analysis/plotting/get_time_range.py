import os
import argparse
import pandas as pd


def get_arg():
    parser = argparse.ArgumentParser(description="Get time range from a CSV file.")
    parser.add_argument(
        "-f",
        "--path",
        "--csv_file",
        type=str,
        required=True,
        help="Path to the CSV file containing time data.",
    )
    parser.add_argument(
        "-tz",
        "--timezone",
        type=str,
        default="Asia/Hong_Kong",
        help="Timezone to convert the timestamps to. Default is Asia/Hong_Kong.",
    )
    args = parser.parse_args()
    return args


def get_time_range(csv_file):
    if not os.path.exists(csv_file):
        raise FileNotFoundError(f"The file {csv_file} does not exist.")

    COL_NAMES = [
        "end_ts",
        "request_id",
        "parent_service_name",
        "service_name",
        "delay",
        "call_id",
        "parent_call_id",
    ]
    df = pd.read_csv(csv_file, names=COL_NAMES)
    if df.empty:
        return {"start_time": None, "end_time": None}

    df["start_time"] = df["end_ts"] - df["delay"]
    time_range = {
        "start_time": df["start_time"].min(),
        "end_time": df["end_ts"].max(),
    }
    return time_range


def get_time_range_from_multiple_files(csv_files):
    all_start_times = []
    all_end_times = []

    for csv_file in csv_files:
        time_range = get_time_range(csv_file)
        all_start_times.append(time_range["start_time"])
        all_end_times.append(time_range["end_time"])

    # exclude None values
    all_start_times = [t for t in all_start_times if t is not None]
    all_end_times = [t for t in all_end_times if t is not None]

    return {
        "start_time": min(all_start_times),
        "end_time": max(all_end_times),
    }


if __name__ == "__main__":
    args = get_arg()
    if os.path.isdir(args.path):
        csv_files = []
        # walk through the directory and find all CSV files
        for root, _, files in os.walk(args.path):
            for file in files:
                if file.endswith(".csv"):
                    if not root.endswith("stats"):
                        csv_files.append(os.path.join(root, file))
        if not csv_files:
            raise FileNotFoundError(f"No CSV files found in the directory {args.path}.")
        # get time range from multiple CSV files
        print(f"Found {len(csv_files)} CSV files in the directory.")
        time_range = get_time_range_from_multiple_files(csv_files)
    else:
        time_range = get_time_range(args.path)
    # print(f"Time Range: {time_range['start_time']} to {time_range['end_time']}")

    # convert the timestamp to datetime format
    start_time = pd.to_datetime(time_range["start_time"], unit="s", utc=True)
    end_time = pd.to_datetime(time_range["end_time"], unit="s", utc=True)
    if args.timezone:
        if args.timezone.lower() == "original":
            pass
        else:
            # convert to specified timezone
            start_time = start_time.tz_convert(args.timezone)
            end_time = end_time.tz_convert(args.timezone)
    else:
        # default to Asia/Hong_Kong timezone
        start_time = start_time.tz_convert("Asia/Hong_Kong")
        end_time = end_time.tz_convert("Asia/Hong_Kong")
    # format until seconds
    start_time = start_time.strftime("%Y-%m-%d %H:%M:%S")
    end_time = end_time.strftime("%Y-%m-%d %H:%M:%S")
    print(f"Formatted Time Range: {start_time} to {end_time}")
