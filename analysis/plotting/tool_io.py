import os, sys
import pandas as pd
from pandas import DataFrame
from typing import Optional, Dict
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from tqdm import tqdm
from difflib import SequenceMatcher
from typing import List, Tuple


module_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
print(f"Adding module path: {module_path}")
if module_path not in sys.path:
    print(f"Appending {module_path} to sys.path")
    sys.path.append(module_path)

from module_io.io_data import (
    parse_task_io_data,
    parse_module_io_data,
    parse_modules_io_data,
)
from plotting.utils import shorten_task_name


def plot_io_size(
    data: DataFrame,
    tag: str = "test_agent",
    protocal: str = "json_str",
    save_dir: Optional[str] = "./plots",
    **kwargs: Dict[str, str],
) -> str:
    """Plot scatter size of input and output for each task in an agent.
            The size can be calcuated in terms of the length of json-str, or in terms of the size of the bytes. The latter will be much smaller due to smaller size when representing float number
    Args:
            data: pd.DataFrame with (at least) columns [request_id: str, task_name: str, input_size_in_json_str: int, input_size_in_bytes: int, output_size_in_json_str: int, output_size_in_bytes: int]
            protocal: str, if json_str, use size_in_json_str for plotting, if "bytes", use size_in_bytes instead.
            tag: str, tag for the agent or task.
            save_dir: str, directory to save the plots. If None, will use the current directory.
            **kwargs: additional arguments for plotting, such as figure size, title, etc.
    Return:
            fig_path: path to the figure.
    """
    if protocal not in ["json_str", "bytes"]:
        raise ValueError(
            f"Invalid protocol: {protocal}. Must be 'json_str' or 'bytes'."
        )

    # Select the appropriate size columns based on the protocol
    input_size_col = f"input_size_in_{protocal}"
    output_size_col = f"output_size_in_{protocal}"

    if data.empty:
        print(f"No data found for tag: {tag}")
        return ""

    input_size_unit = kwargs.get("input_size_unit", "Bytes")
    if input_size_unit == "Bytes":
        pass
    elif input_size_unit == "KB":
        data[input_size_col] /= 1024
    elif input_size_unit == "MB":
        data[input_size_col] /= 1024 * 1024
    elif input_size_unit == "GB":
        data[input_size_col] /= 1024 * 1024 * 1024
    else:
        raise ValueError(
            f"Invalid input size unit: {input_size_unit}. Must be 'KB', 'MB', or 'GB'."
        )
    output_size_unit = kwargs.get("output_size_unit", "Bytes")
    if output_size_unit == "Bytes":
        pass
    elif output_size_unit == "KB":
        data[output_size_col] /= 1024
    elif output_size_unit == "MB":
        data[output_size_col] /= 1024 * 1024
    elif output_size_unit == "GB":
        data[output_size_col] /= 1024 * 1024 * 1024
    else:
        raise ValueError(
            f"Invalid output size unit: {output_size_unit}. Must be 'KB', 'MB', or 'GB'."
        )

    # Shorten task names for better readability
    data = shorten_task_name(data)
    task_names = data["task_name"].unique()
    task_names = sorted(task_names)  # Sort task names for consistent ordering
    colors = plt.cm.tab10(np.linspace(0, 1, len(task_names)))

    # Create a scatter plot
    plt.figure(figsize=(20, 12))
    sns.scatterplot(
        x=data[input_size_col],
        y=data[output_size_col],
        hue=data["task_name"],
        # palette="viridis",
        palette=colors,
        s=100,
        alpha=0.7,
    )

    log_scale = kwargs.get("log_scale", False)
    if log_scale:
        plt.xscale("log")
        plt.yscale("log")

    plt.title(f"Input vs Output Size for {tag} ({protocal})")
    plt.xlabel(f"Input Size in {input_size_unit} ({protocal})")
    plt.ylabel(f"Output Size in {output_size_unit} ({protocal})")
    plt.grid(True)

    # Save the figure
    filename = f"{tag}_io_size_{protocal}.png"
    if log_scale:
        filename = filename.replace(".png", "_logscale.png")
    fig_path = os.path.join(save_dir, filename)
    plt.savefig(fig_path)
    plt.close()

    return fig_path


def plot_task_io_dist(
    data: DataFrame,
    tag: str = "test_agent",
    protocal: str = "json_str",
    plot_type: str = "hist2d",
    save_dir: Optional[str] = "./plots",
    **kwargs: Dict[str, str],
) -> str:
    """Plot distribution of input and output size for each task in an agent. One subplot for each task.
            The size can be calcuated in terms of the length of json-str, or in terms of the size of the bytes. The latter will be much smaller due to smaller size when representing float number
    Args:
            data: pd.DataFrame with (at least) columns [request_id: str, task_name: str, input_size_in_json_str: int, input_size_in_bytes: int, output_size_in_json_str: int, output_size_in_bytes: int]
            protocal: str, if json_str, use size_in_json_str for plotting, if "bytes", use size_in_bytes instead.
            tag: str, tag for the agent or task.
            plot_type: str, type of plot to use. Can be "hist2d" for 2D histogram or "heatmap" for heatmap, or "hexbin" for hexbin plot.
            save_dir: str, directory to save the plots. If None, will use the current directory.
            **kwargs: additional arguments for plotting, such as figure size, title, etc.
    Return:
            fig_path: path to the figure.
    """
    if protocal not in ["json_str", "bytes"]:
        raise ValueError(
            f"Invalid protocol: {protocal}. Must be 'json_str' or 'bytes'."
        )

    # Select the appropriate size columns based on the protocol
    input_size_col = f"input_size_in_{protocal}"
    output_size_col = f"output_size_in_{protocal}"

    if data.empty:
        print(f"No data found for tag: {tag}")
        return ""

    input_size_unit = kwargs.get("input_size_unit", "Bytes")
    if input_size_unit == "Bytes":
        pass
    elif input_size_unit == "KB":
        data[input_size_col] /= 1024
    elif input_size_unit == "MB":
        data[input_size_col] /= 1024 * 1024
    elif input_size_unit == "GB":
        data[input_size_col] /= 1024 * 1024 * 1024
    else:
        raise ValueError(
            f"Invalid input size unit: {input_size_unit}. Must be 'KB', 'MB', or 'GB'."
        )
    output_size_unit = kwargs.get("output_size_unit", "Bytes")
    if output_size_unit == "Bytes":
        pass
    elif output_size_unit == "KB":
        data[output_size_col] /= 1024
    elif output_size_unit == "MB":
        data[output_size_col] /= 1024 * 1024
    elif output_size_unit == "GB":
        data[output_size_col] /= 1024 * 1024 * 1024
    else:
        raise ValueError(
            f"Invalid output size unit: {output_size_unit}. Must be 'KB', 'MB', or 'GB'."
        )

    # Shorten task names for better readability
    data = shorten_task_name(data)
    task_names = data["task_name"].unique()
    task_names = sorted(task_names)  # Sort task names for consistent ordering
    colors = plt.cm.tab10(np.linspace(0, 1, len(task_names)))

    n_tasks = len(task_names)
    # Setup subplot grid
    ncols = 2 if n_tasks > 2 else 1
    if n_tasks >= 6:
        ncols = 3
    if n_tasks >= 12:
        ncols = 4
    nrows = (n_tasks + ncols - 1) // ncols

    fig, axes = plt.subplots(
        nrows, ncols, figsize=(6 * ncols, 4 * nrows), squeeze=False
    )
    axes = axes.flatten()

    log_scale = kwargs.get("log_scale", False)
    for i, task_name in enumerate(task_names):
        task_data = data[data["task_name"] == task_name]
        if task_data.empty:
            print(f"No data found for task: {task_name}")
            continue

        if plot_type == "hist2d":
            sns.histplot(
                task_data,
                ax=axes[i],
                x=input_size_col,
                y=output_size_col,
                bins=50,
                # log_scale=(True, True),
                cbar=True,
                cbar_kws=dict(shrink=0.75),
            )
        elif plot_type == "heatmap":
            heatmap_data, xedges, yedges = np.histogram2d(
                task_data[input_size_col],
                task_data[output_size_col],
                bins=50,
            )
            heatmap_data = np.ma.masked_where(heatmap_data == 0, heatmap_data)
            sns.heatmap(
                heatmap_data.T,
                ax=axes[i],
                cmap="Blues",
                cbar_kws={"label": "Count"},
                xticklabels=xedges,
                yticklabels=yedges,
            )
            if log_scale:
                axes[i].set_xscale("log")
                axes[i].set_yscale("log")
        elif plot_type == "hexbin":
            axes[i].hexbin(
                task_data[input_size_col],
                task_data[output_size_col],
                gridsize=50,
                cmap="Blues",
                mincnt=1,  # Avoid zero counts
            )
            if log_scale:
                axes[i].set_xscale("log")
                axes[i].set_yscale("log")
        else:
            raise ValueError(
                f"Invalid plot type: {plot_type}. Must be 'hist2d', 'heatmap', or 'hexbin'."
            )
        axes[i].set_title(f"{task_name} ({protocal})")
        axes[i].set_xlabel(f"Input Size in {input_size_unit} ({protocal})")
        axes[i].set_ylabel(f"Output Size in {output_size_unit} ({protocal})")
        axes[i].grid(True)
    # Remove empty subplots
    for j in range(i + 1, len(axes)):
        fig.delaxes(axes[j])
    plt.tight_layout()
    plt.suptitle(f"Input and Output Size Heatmap for {tag} ({protocal})", y=1.02)
    # Save the figure
    filename = f"{tag}_task_io_{plot_type}_{protocal}.png"
    if log_scale:
        filename = filename.replace(".png", "_logscale.png")
    fig_path = os.path.join(save_dir, filename)
    plt.savefig(fig_path)
    plt.close()
    return fig_path


def get_args():
    import argparse

    parser = argparse.ArgumentParser(
        description="Plot LLM prompt generation distribution"
    )
    parser.add_argument(
        "--tag", type=str, default="test_agent", help="Tag for the agent or task"
    )
    parser.add_argument(
        "--module_dirs",
        type=str,
        nargs="+",
        required=True,
        help="Directory containing the module files",
    )
    parser.add_argument(
        "--protocal",
        type=str,
        default="json_str",
        choices=["json_str", "bytes"],
        help="Protocol to use for size calculation (json_str or bytes)",
    )
    parser.add_argument(
        "--log_scale",
        action="store_true",
        help="Use logarithmic scale for the plot",
    )
    parser.add_argument(
        "--no_cache",
        action="store_true",
        help="If set, will not use cached data and will parse the module IO data again",
    )
    parser.add_argument(
        "--include_tasks",
        type=str,
        nargs="*",
        default=None,
        help="List of task names to include in the plot. If None, all tasks will be included.",
    )
    parser.add_argument(
        "--exclude_tasks",
        type=str,
        nargs="*",
        default=None,
        help="List of task names to exclude from the plot. If None, no tasks will be excluded.",
    )
    parser.add_argument(
        "--save_dir",
        type=str,
        default="./plots",
        help="Directory to save the plots",
    )
    parser.add_argument(
        "--context_name",
        type=str,
        default="context",
        help="Name of the context field in the request JSON",
    )
    return parser.parse_args()


def main():
    args = get_args()
    tag = args.tag
    module_dirs = args.module_dirs
    protocal = args.protocal
    save_dir = args.save_dir
    log_scale = args.log_scale

    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    cache_dir = os.path.join(save_dir, ".cache")
    os.makedirs(cache_dir, exist_ok=True)
    data_save_path = os.path.join(cache_dir, f"{tag}_tool_io_data.csv")
    if os.path.exists(data_save_path) and not args.no_cache:
        print(f"Data already exists at {data_save_path}. Loading from file.")
        data = pd.read_csv(data_save_path)
        if len(data) == 0:
            print(f"No data found for tag: {tag}")
            return
    else:
        print(f"Data not found at {data_save_path}. Parsing module IO data.")
        # Parse the module IO data
        module_data: Dict[str, DataFrame] = parse_modules_io_data(
            module_dirs, context_name=args.context_name
        )

        # concat all dataframes into one
        data: DataFrame = pd.concat(module_data.values(), ignore_index=True)
        # save concatenated dataframe
        data.to_csv(data_save_path, index=False)

    if len(data) == 0:
        print(f"No data found for tag: {tag}")
        return

    include_tasks = args.include_tasks
    exclude_tasks = args.exclude_tasks

    if include_tasks is not None:
        print(f"Including only tasks: {include_tasks}")
        task_names = data["task_name"].unique()
        selected_tasks = [
            task for task in task_names if any(name in task for name in include_tasks)
        ]
        if not selected_tasks:
            print(f"No matching tasks found in the data for: {include_tasks}")
            return
        data = data[data["task_name"].isin(selected_tasks)]
    if exclude_tasks is not None:
        print(f"Excluding tasks: {exclude_tasks}")
        task_names = data["task_name"].unique()
        selected_tasks = [
            task
            for task in task_names
            if not any(name in task for name in exclude_tasks)
        ]
        if not selected_tasks:
            print(f"No tasks left after exclusion: {exclude_tasks}")
            return
        data = data[data["task_name"].isin(selected_tasks)]

    # Plot the IO size
    fig_path = plot_io_size(
        data,
        tag,
        protocal,
        save_dir=save_dir,
        log_scale=log_scale,
        input_size_unit="KB",
        output_size_unit="KB",
    )
    print(f"Plots saved to {fig_path}")
    # Plot the task IO heatmap
    heatmap_fig_path = plot_task_io_dist(
        data,
        tag,
        protocal,
        plot_type="hist2d",
        save_dir=save_dir,
        log_scale=True,
        # input_size_unit="KB",
        # output_size_unit="KB",
    )
    print(f"Heatmap saved to {heatmap_fig_path}")


if __name__ == "__main__":
    main()
