#!/bin/bash

args="$@"
if [ -z "$args" ]; then
    echo "No additional arguments provided. $args"
else
    echo "Running delay analysis with arguments: $args"
fi

run_swe_env(){
    # This should be run on weicpu1 server because data are on weicpu1
    # or we can run on h20, with updated prometheus-url.
    python resource.py \
        --prometheus-url http://localhost:9090 \
        --start-time "2025-07-26 22:39:22" \
        --end-time "2025-07-27 16:13:30" \
        --data-dir ./.cache/data/prometheus/swe-env \
        --tag bsl \
        --include-containers "sweagent" "agbench" \
        --exclude-containers "server" "model" "SWEEnv" "fixed-interval" \
        --aggregate-containers "sweagent-swe-agent" \
        --max-containers 5 \
        --generate-report \
        --save-dir ./.cache/plots $args

    python resource.py \
        --prometheus-url http://localhost:9090 \
        --start-time "2025-07-24 08:11:26" \
        --end-time "2025-07-24 09:12:32" \
        --data-dir ./.cache/data/prometheus/swe-env \
        --tag 512threads-rps500 \
        --include-containers "sweagent" "agbench" \
        --exclude-containers "server" "model" "SWEEnv" \
        --aggregate-containers "sweagent-swe-agent" \
        --max-containers 5 \
        --generate-report \
        --save-dir ./.cache/plots $args

    python resource.py \
        --prometheus-url http://localhost:9090 \
        --start-time "2025-07-24 09:13:09" \
        --end-time "2025-07-24 10:30:01" \
        --data-dir ./.cache/data/prometheus/swe-env \
        --tag 256threads-rps500 \
        --include-containers "sweagent" "agbench" \
        --exclude-containers "server" "model" "SWEEnv" "env-module-512-threads" \
        --aggregate-containers "sweagent-swe-agent" \
        --max-containers 5 \
        --generate-report \
        --save-dir ./.cache/plots $args

    python resource.py \
        --prometheus-url http://localhost:9090 \
        --start-time "2025-07-24 10:30:37" \
        --end-time "2025-07-24 11:22:18" \
        --data-dir ./.cache/data/prometheus/swe-env \
        --tag 128threads-rps500 \
        --include-containers "sweagent" "agbench" \
        --exclude-containers "server" "model" "SWEEnv" "env-module-256-threads" \
        --aggregate-containers "sweagent-swe-agent" \
        --max-containers 5 \
        --generate-report \
        --save-dir ./.cache/plots $args

    python resource.py \
        --prometheus-url http://localhost:9090 \
        --start-time "2025-07-24 11:22:54" \
        --end-time "2025-07-24 12:18:44" \
        --data-dir ./.cache/data/prometheus/swe-env \
        --tag 64threads-rps500 \
        --include-containers "sweagent" "agbench" \
        --exclude-containers "server" "model" "SWEEnv" "env-module-128-threads" \
        --aggregate-containers "sweagent-swe-agent" \
        --max-containers 5 \
        --generate-report \
        --save-dir ./.cache/plots $args

    python resource.py \
        --prometheus-url http://localhost:9090 \
        --start-time "2025-07-24 12:19:20" \
        --end-time "2025-07-24 13:25:12" \
        --data-dir ./.cache/data/prometheus/swe-env \
        --tag 32threads-rps500 \
        --include-containers "sweagent" "agbench" \
        --exclude-containers "server" "model" "SWEEnv" "env-module-64-threads" \
        --aggregate-containers "sweagent-swe-agent" \
        --max-containers 5 \
        --generate-report \
        --save-dir ./.cache/plots $args

    python resource.py \
        --prometheus-url http://localhost:9090 \
        --start-time "2025-07-25 08:57:29" \
        --end-time "2025-07-25 11:44:18" \
        --data-dir ./.cache/data/prometheus/swe-env \
        --tag 32threads-rps0.05 \
        --include-containers "sweagent" "agbench" \
        --exclude-containers "server" "model" "SWEEnv" \
        --aggregate-containers "sweagent-swe-agent" \
        --max-containers 5 \
        --generate-report \
        --save-dir ./.cache/plots $args

    python resource.py \
        --prometheus-url http://localhost:9090 \
        --start-time "2025-07-25 07:25:06" \
        --end-time "2025-07-25 08:51:31" \
        --data-dir ./.cache/data/prometheus/swe-env \
        --tag 32threads-rps0.1 \
        --include-containers "sweagent" "agbench" \
        --exclude-containers "server" "model" "SWEEnv" "fixed-interval-0-15" \
        --aggregate-containers "sweagent-swe-agent" \
        --max-containers 5 \
        --generate-report \
        --save-dir ./.cache/plots $args

    python resource.py \
        --prometheus-url http://localhost:9090 \
        --start-time "2025-07-25 06:04:00" \
        --end-time "2025-07-25 07:21:49" \
        --data-dir ./.cache/data/prometheus/swe-env \
        --tag 32threads-rps0.15 \
        --include-containers "sweagent" "agbench" \
        --exclude-containers "server" "model" "SWEEnv" \
        --aggregate-containers "sweagent-swe-agent" \
        --max-containers 5 \
        --generate-report \
        --save-dir ./.cache/plots $args

    python resource.py \
        --prometheus-url http://localhost:9090 \
        --start-time "2025-07-25 04:10:10" \
        --end-time "2025-07-25 06:01:11" \
        --data-dir ./.cache/data/prometheus/swe-env \
        --tag 32threads-rps0.2 \
        --include-containers "sweagent" "agbench" \
        --exclude-containers "server" "model" "SWEEnv" \
        --aggregate-containers "sweagent-swe-agent" \
        --max-containers 5 \
        --generate-report \
        --save-dir ./.cache/plots $args

    python resource.py \
        --prometheus-url http://localhost:9090 \
        --start-time "2025-07-24 13:25:48" \
        --end-time "2025-07-24 15:03:07" \
        --data-dir ./.cache/data/prometheus/swe-env \
        --tag 16threads-rps500 \
        --include-containers "sweagent" "agbench" \
        --exclude-containers "server" "model" "SWEEnv" "env-module-32-threads" \
        --aggregate-containers "sweagent-swe-agent" \
        --max-containers 5 \
        --generate-report \
        --save-dir ./.cache/plots $args

    python resource.py \
        --prometheus-url http://localhost:9090 \
        --start-time "2025-07-28 01:47:24" \
        --end-time "2025-07-28 04:15:41" \
        --data-dir ./.cache/data/prometheus/swe-env \
        --tag 8threads-rps500 \
        --include-containers "sweagent" "agbench" \
        --exclude-containers "server" "model" "SWEEnv" "env-module-16-threads" \
        --aggregate-containers "sweagent-swe-agent" \
        --max-containers 5 \
        --generate-report \
        --save-dir ./.cache/plots $args

    python resource.py \
        --prometheus-url http://localhost:9090 \
        --start-time "2025-07-28 04:16:06" \
        --end-time "2025-07-28 08:47:42" \
        --data-dir ./.cache/data/prometheus/swe-env \
        --tag 4threads-rps500 \
        --include-containers "sweagent" "agbench" \
        --exclude-containers "server" "model" "SWEEnv" "env-module-8-threads" \
        --aggregate-containers "sweagent-swe-agent" \
        --max-containers 5 \
        --generate-report \
        --save-dir ./.cache/plots $args
}


run_hgpt(){
    python resource.py \
    --prometheus-url http://localhost:9090 \
    --start-time "2025-06-06 07:37:41" \
    --end-time "2025-06-06 10:58:23" \
    --data-dir ./.cache/data/prometheus/hgpt \
    --tag one-by-one \
    --include-containers "hugginggpt" \
    --exclude-containers "swe" \
    --include-gpus 7 \
    --max-containers 10 \
    --generate-report \
    --save-dir ./.cache/plots/hgpt $args
}

run_moa(){
    python resource.py \
    --prometheus-url http://localhost:9090 \
    --start-time "2025-06-13 11:04:58" \
    --end-time "2025-06-13 23:38:19" \
    --data-dir ./.cache/data/prometheus/moa \
    --tag one-by-one \
    --include-containers "moa" \
    --exclude-containers "swe" \
    --include-gpus 7 \
    --max-containers 10 \
    --generate-report \
    --save-dir ./.cache/plots/moa $args
}

# run_hgpt
# run_moa

run_dynamic_rag(){
    python resource.py \
    --prometheus-url http://localhost:9090 \
    --start-time "2025-07-17 07:32:26" \
    --end-time "2025-07-17 10:00:51" \
    --data-dir ./.cache/data/prometheus/dynamic_rag \
    --tag one-by-one \
    --include-containers "agbench-dynamic_rag" \
    --exclude-containers "swe" \
    --include-gpus 1 2 \
    --max-containers 10 \
    --generate-report \
    --save-dir ./.cache/plots/dynamic_rag $args
}


run_naive_rag(){
    python resource.py \
    --prometheus-url http://localhost:9090 \
    --start-time "2025-07-16 17:44:25" \
    --end-time "2025-07-16 20:20:12" \
    --data-dir ./.cache/data/prometheus/naive_rag \
    --tag one-by-one \
    --include-containers "agbench-naive_rag" \
    --exclude-containers "swe" \
    --include-gpus 1 2 \
    --max-containers 10 \
    --generate-report \
    --save-dir ./.cache/plots/naive_rag $args
}

# run_dynamic_rag
# run_naive_rag
