# /mnt/data/agbench/results/rag/dynamic_rag/opt/baseline-temp07/task-llm-task-embed/bsl_dynamic-WQA-FixedInterval-8.0-10000

args="$@"
if [ -z "$args" ]; then
    echo "No additional arguments provided. $args"
else
    echo "Running delay analysis with arguments: $args"
fi

DRAG_DATA_HOME=/mnt/data/agbench/results/rag/dynamic_rag
DRAG_PLOT_HOME=./plots/dynamic_rag


run_dynamic_rag_io(){
    python llm_io.py --tag dynamic_rag \
        --vllm_metrics_dirs $DRAG_DATA_HOME/stage-1/dynamic-WQA-OneByOne-0.0-10000/results/vllm-llm/vllm_request_metrics \
        --agent_metrics_files $DRAG_DATA_HOME/stage-1/dynamic-WQA-OneByOne-0.0-10000/results/server/monitor/rag_server/e2e_delay.csv \
        --save_dir $DRAG_PLOT_HOME/llm_io
        
    python tool_io.py --tag dynamic_rag \
        --module_dirs $DRAG_DATA_HOME/stage-1/dynamic-WQA-OneByOne-0.0-10000/results/server/internal/intermediate/requests \
        --protocal json_str \
        --include_tasks load_docs split_docs embed_documents vdb_store embed_query vdb_search reranking \
        --log_scale \
        --save_dir $DRAG_PLOT_HOME/tool_io
}


run_dynamic_rag_delay(){
    exps=(
        "opt/baseline-temp07/shared-llm/bsl_dynamic-WQA-FixedInterval-8.0-10000"
        "opt/baseline-temp07/shared-llm/bsl_dynamic-WQA-FixedInterval-16.0-10000"
        "opt/baseline-temp07/shared-llm/bsl_dynamic-WQA-FixedInterval-32.0-10000"
    )
    for exp in "${exps[@]}"; do
        echo "Processing experiment: $exp"
        data_home="$DRAG_DATA_HOME/$exp"
        save_home="$DRAG_PLOT_HOME/delay/$exp"
        
        # Dynamic RAG Delay Analysis Script
        python delay.py --tag dynamic_rag \
            --instance_dirs $data_home/results/client/monitor/rag_client \
                            $data_home/results/server/monitor/rag_server \
            --include_route \
            --include_delay_types e2e \
            --save_dir $save_home/dynamic_rag/original $args
        
        python delay.py --tag dynamic_rag \
            --instance_dirs $data_home/results/client/monitor/rag_client \
                            $data_home/results/server/monitor/rag_server \
            --include_route \
            --include_delay_types e2e \
            --merge_task \
            --save_dir $save_home/dynamic_rag/merged $args
        
        # --include_tasks retrieval_judger query_translation direct_generation normal_generation load_docs split_docs embed_documents vdb_store embed_query vdb_search reranking \
        
        python delay.py --tag dynamic_rag-e2e \
            --instance_dirs $data_home/results/client/monitor/rag_client \
                            $data_home/results/server/monitor/rag_server \
            --include_sources rag_client \
            --include_route \
            --save_dir $save_home/dynamic_rag-e2e/original $args
        
        python delay.py --tag dynamic_rag-e2e \
            --instance_dirs $data_home/results/client/monitor/rag_client \
                            $data_home/results/server/monitor/rag_server \
            --include_sources rag_client \
            --merge_task \
            --save_dir $save_home/dynamic_rag-e2e/merged $args
    done
}

run_dynamic_rag_rps_delay(){
    exps=(
        "opt/baseline-temp07/shared-llm/bsl_dynamic-WQA-FixedInterval-8.0-10000"
        "opt/baseline-temp07/shared-llm/bsl_dynamic-WQA-FixedInterval-16.0-10000"
        "opt/baseline-temp07/shared-llm/bsl_dynamic-WQA-FixedInterval-32.0-10000"
    )
    instance_dirs=()
    instance_groups=()
    for exp in "${exps[@]}"; do
        echo "Processing experiment: $exp"
        data_home="$DRAG_DATA_HOME/$exp"
        save_home="$DRAG_PLOT_HOME/multi_delay/$exp"
        
        # Collect instance directories and groups
        instance_dirs+=("$data_home/results/client/monitor/rag_client")
        instance_dirs+=("$data_home/results/server/monitor/rag_server")

        # if exp is stage-2-nolimit, add "bsl" to instance_groups
        if [[ "$exp" == "stage-2-nolimit" ]]; then
            instance_groups+=("bsl")
        else
            # otherwise, add "nthreads" with threads value and "rps" with the rps value extracted from the exp string
            # threads=$(echo "$exp" | grep -oP '(?<=/)[0-9]+(?=threads)')
            # rps=$(echo "$exp" | grep -oP '(?<=rps)[0-9]+')
            # instance_groups+=("nthreads=$threads;rps=$rps")
            
            # extract rps, like FixedInterval-{rps:float}-10000
            rps=$(echo "$exp" | grep -oP '(?<=FixedInterval-)[0-9]+[.][0-9]+(?=-10000)')
            instance_groups+=("rps=$rps")
            instance_groups+=("rps=$rps")
        fi
    done


    # concatenate instance directories into a single string
    instance_dirs_str=$(printf " %s" "${instance_dirs[@]}")
    # concatenate instance groups into a single string
    instance_groups_str=$(printf " %s" "${instance_groups[@]}")

    echo "instance_dirs_str: $instance_dirs_str"
    echo "instance_groups_str: $instance_groups_str"

    python delay.py --tag dynamic_rag \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --include_route \
        --include_delay_types e2e \
        --save_dir $DRAG_PLOT_HOME/multi_delay/dynamic_rag/original $args
    
    python delay.py --tag dynamic_rag \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --include_route \
        --include_delay_types e2e \
        --merge_task \
        --save_dir $DRAG_PLOT_HOME/multi_delay/dynamic_rag/merged $args
    
    python delay.py --tag dynamic_rag-e2e \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --include_sources rag_client \
        --include_route \
        --save_dir $DRAG_PLOT_HOME/multi_delay/dynamic_rag-e2e/original $args
    
    python delay.py --tag dynamic_rag-e2e \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --include_sources rag_client \
        --include_route \
        --merge_task \
        --save_dir $DRAG_PLOT_HOME/multi_delay/dynamic_rag-e2e/merged $args
}

run_dynamic_rag_resource(){
    excluded_containers=(
        "cadvisor" "grafana" "prometheus"
        "hgx" "li_triton"
        "fred-pytorch-container"
        "lm-eval" "longcontext-LLaDA"
        "peaceful_matsumoto"
    )
    excluded_containers_str=$(printf " %s" "${excluded_containers[@]}")
    aggregated_containers=(
        "dynamic_ragagent-dynamic_rag-agent-latest"
    )
    aggregated_containers_str=$(printf " %s" "${aggregated_containers[@]}")

    # RPS8 : Formatted Time Range: 2025-08-13 11:11:45 to 2025-08-13 11:18:23
    # RPS16: Formatted Time Range: 2025-08-13 11:05:33 to 2025-08-13 11:09:25
    # RPS32: Formatted Time Range: 2025-08-13 10:59:19 to 2025-08-13 11:03:13

    python resource.py \
    --prometheus-url http://localhost:9090 \
    --start-time "2025-08-13 11:11:45" \
    --end-time "2025-08-13 11:18:23" \
    --step 5 \
    --data-dir $DRAG_PLOT_HOME/.cache/prometheus \
    --include-containers "agbench" \
    --exclude-containers $excluded_containers_str \
    --aggregate-containers $aggregated_containers_str \
    --include-gpus 0 1 2 3 \
    --tag rps8 \
    --max-containers 8 \
    --generate-report \
    --save-dir $DRAG_PLOT_HOME/resource $args

    python resource.py \
    --prometheus-url http://localhost:9090 \
    --start-time "2025-08-13 11:05:33" \
    --end-time "2025-08-13 11:09:25" \
    --step 5 \
    --data-dir $DRAG_PLOT_HOME/.cache/prometheus \
    --include-containers "agbench" \
    --exclude-containers $excluded_containers_str \
    --aggregate-containers $aggregated_containers_str \
    --include-gpus 0 1 2 3 \
    --tag rps16 \
    --max-containers 8 \
    --generate-report \
    --save-dir $DRAG_PLOT_HOME/resource $args

    python resource.py \
    --prometheus-url http://localhost:9090 \
    --start-time "2025-08-13 10:59:19" \
    --end-time "2025-08-13 11:03:13" \
    --step 5 \
    --data-dir $DRAG_PLOT_HOME/.cache/prometheus \
    --include-containers "agbench" \
    --exclude-containers $excluded_containers_str \
    --aggregate-containers $aggregated_containers_str \
    --include-gpus 0 1 2 3 \
    --tag rps32 \
    --max-containers 8 \
    --generate-report \
    --save-dir $DRAG_PLOT_HOME/resource $args


    # Formatted Time Range: 2025-08-13 19:15:59 to 2025-08-13 19:20:03
    python resource.py \
    --prometheus-url http://localhost:9090 \
    --start-time "2025-08-13 19:15:59" \
    --end-time "2025-08-13 19:20:03" \
    --step 5 \
    --data-dir $DRAG_PLOT_HOME/.cache/prometheus \
    --include-containers "agbench" \
    --exclude-containers $excluded_containers_str \
    --aggregate-containers $aggregated_containers_str \
    --include-gpus 0 1 2 3 \
    --tag task-llm-task-embed-rps32 \
    --max-containers 8 \
    --generate-report \
    --save-dir $DRAG_PLOT_HOME/resource $args

    # Formatted Time Range: 2025-08-15 11:41:40 to 2025-08-15 11:43:53
    python resource.py \
    --prometheus-url http://localhost:9090 \
    --start-time "2025-08-15 11:41:40" \
    --end-time "2025-08-15 11:43:53" \
    --step 5 \
    --data-dir $DRAG_PLOT_HOME/.cache/prometheus \
    --include-containers "agbench" \
    --exclude-containers $excluded_containers_str \
    --aggregate-containers $aggregated_containers_str \
    --include-gpus 0 1 2 3 \
    --tag task-llm-task-embed-async-rps32 \
    --max-containers 8 \
    --generate-report \
    --save-dir $DRAG_PLOT_HOME/resource $args

}

# run_dynamic_rag_io
run_dynamic_rag_delay
# run_dynamic_rag_rps_delay
# run_dynamic_rag_resource

