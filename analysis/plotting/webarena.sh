#!/bin/bash

args="$@"
if [ -z "$args" ]; then
    echo "No additional arguments provided. $args"
else
    echo "Running delay analysis with arguments: $args"
fi

WEBARENA_DATA_HOME=/home/<USER>/ai-agent-benchmark/webarena/.cache/webarena/multiproc/monitor
WEBARENA_PLOT_HOME=./plots/webarena

run_webarena_io(){
    python llm_io.py --tag webarena \
        --vllm_metrics_dirs $WEBARENA_DATA_HOME/trace/vllm \
        --agent_metrics_files $WEBARENA_DATA_HOME/trace/llm/e2e_delay.csv \
        --save_dir $WEBARENA_PLOT_HOME/llm_io

    python tool_io.py --tag webarena \
        --module_dirs /home/<USER>/ai-agent-benchmark/webarena/.cache/webarena/multiproc/intermediate/execution_coordinator/requests \
        /home/<USER>/ai-agent-benchmark/webarena/.cache/webarena/multiproc/intermediate/env_manager/requests \
        --protocal json_str \
        --log_scale \
        --context_name fsm_context \
        --save_dir $WEBARENA_PLOT_HOME/tool_io
}


run_webarena_delay(){
    for exp in "obo" "fix_0_0_1" "fix_0_0_2" "fix_0_0_5" "fix_0_1" "fix_0_2"; do
        echo "Processing experiment: $exp"
        data_home="$WEBARENA_DATA_HOME/$exp"
        save_home="$WEBARENA_PLOT_HOME/delay/$exp"

        # WebArena Delay Analysis Script
        python delay.py --tag webarena \
            --instance_dirs $data_home/client \
            $data_home/env_manager \
            $data_home/llm \
            $data_home/execution_coordinator \
            --exclude_tasks e2e_execution \
            --include_route \
            --include_delay_types e2e \
            --save_dir $save_home/webarena/original $args

        python delay.py --tag webarena \
            --instance_dirs $data_home/client \
            $data_home/env_manager \
            $data_home/llm \
            $data_home/execution_coordinator \
            --exclude_tasks e2e_execution \
            --include_route \
            --include_delay_types e2e \
            --merge_task \
            --save_dir $save_home/webarena/merged $args

        python delay.py --tag webarena-e2e \
            --instance_dirs $data_home/client \
            $data_home/env_manager \
            $data_home/llm \
            $data_home/execution_coordinator \
            --include_sources client \
            --include_route \
            --save_dir $save_home/webarena-e2e/original $args

        python delay.py --tag webarena-e2e \
            --instance_dirs $data_home/client \
            $data_home/env_manager \
            $data_home/llm \
            $data_home/execution_coordinator \
            --include_sources client \
            --include_route \
            --merge_task \
            --save_dir $save_home/webarena-e2e/merged $args
    done
}

run_webarena_rps_delay(){
    instance_dirs=(
        $WEBARENA_DATA_HOME/obo/client
        $WEBARENA_DATA_HOME/obo/env_manager
        $WEBARENA_DATA_HOME/obo/execution_coordinator
        $WEBARENA_DATA_HOME/obo/llm
        $WEBARENA_DATA_HOME/fix_0_0_1/client
        $WEBARENA_DATA_HOME/fix_0_0_1/env_manager
        $WEBARENA_DATA_HOME/fix_0_0_1/execution_coordinator
        $WEBARENA_DATA_HOME/fix_0_0_1/llm
        $WEBARENA_DATA_HOME/fix_0_0_2/client
        $WEBARENA_DATA_HOME/fix_0_0_2/env_manager
        $WEBARENA_DATA_HOME/fix_0_0_2/execution_coordinator
        $WEBARENA_DATA_HOME/fix_0_0_2/llm
        $WEBARENA_DATA_HOME/fix_0_0_5/client
        $WEBARENA_DATA_HOME/fix_0_0_5/env_manager
        $WEBARENA_DATA_HOME/fix_0_0_5/execution_coordinator
        $WEBARENA_DATA_HOME/fix_0_0_5/llm
        $WEBARENA_DATA_HOME/fix_0_1/llm
        $WEBARENA_DATA_HOME/fix_0_1/client
        $WEBARENA_DATA_HOME/fix_0_1/env_manager
        $WEBARENA_DATA_HOME/fix_0_1/execution_coordinator
        $WEBARENA_DATA_HOME/fix_0_2/llm
        $WEBARENA_DATA_HOME/fix_0_2/client
        $WEBARENA_DATA_HOME/fix_0_2/env_manager
        $WEBARENA_DATA_HOME/fix_0_2/execution_coordinator
    )
    instance_groups=(
        "bsl"
        "bsl"
        "bsl"
        "bsl"
        "nthreads=32;rps=0.01"
        "nthreads=32;rps=0.01"
        "nthreads=32;rps=0.01"
        "nthreads=32;rps=0.01"
        "nthreads=32;rps=0.02"
        "nthreads=32;rps=0.02"
        "nthreads=32;rps=0.02"
        "nthreads=32;rps=0.02"
        "nthreads=32;rps=0.05"
        "nthreads=32;rps=0.05"
        "nthreads=32;rps=0.05"
        "nthreads=32;rps=0.05"
        "nthreads=32;rps=0.1"
        "nthreads=32;rps=0.1"
        "nthreads=32;rps=0.1"
        "nthreads=32;rps=0.1"
        "nthreads=32;rps=0.2"
        "nthreads=32;rps=0.2"
        "nthreads=32;rps=0.2"
        "nthreads=32;rps=0.2"
    )
    # concatenate instance directories into a single string
    instance_dirs_str=$(printf " %s" "${instance_dirs[@]}")
    # concatenate instance groups into a single string
    instance_groups_str=$(printf " %s" "${instance_groups[@]}")

    python delay.py --tag webarena \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --exclude_tasks e2e_execution \
        --include_route \
        --include_delay_types e2e \
        --save_dir $WEBARENA_PLOT_HOME/multi_delay/webarena/original $args

    python delay.py --tag webarena \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --exclude_tasks e2e_execution \
        --include_route \
        --include_delay_types e2e \
        --merge_task \
        --save_dir $WEBARENA_PLOT_HOME/multi_delay/webarena/merged $args

    python delay.py --tag webarena-e2e \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --include_sources client \
        --include_route \
        --save_dir $WEBARENA_PLOT_HOME/multi_delay/webarena-e2e/original $args

    python delay.py --tag webarena-e2e \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --include_sources client \
        --include_route \
        --merge_task \
        --save_dir $WEBARENA_PLOT_HOME/multi_delay/webarena-e2e/merged $args
}

run_webarena_resource(){
    excluded_containers=(
        "agbench"  "hgx" "li_triton"
        "cadvisor" "grafana" "prometheus"
        "fred-pytorch-container"
        "lm-eval" "longcontext-LLaDA"
        "peaceful_matsumoto"
    )
    excluded_containers_str=$(printf " %s" "${excluded_containers[@]}")

    python resource.py \
        --prometheus-url http://localhost:9090 \
        --start-time "2025-07-24 08:10:45" \
        --end-time "2025-07-24 16:52:10" \
        --data-dir $WEBARENA_PLOT_HOME/.cache/prometheus \
        --include-containers "webarena" \
        --exclude-containers $excluded_containers_str \
        --include-gpus 7 \
        --tag bsl \
        --max-containers 5 \
        --generate-report \
        --save-dir $WEBARENA_PLOT_HOME/resource $args

    python resource.py \
        --prometheus-url http://localhost:9090 \
        --start-time "2025-07-26 09:10:17" \
        --end-time "2025-07-26 11:59:18" \
        --data-dir $WEBARENA_PLOT_HOME/.cache/prometheus \
        --include-containers "webarena" \
        --exclude-containers $excluded_containers_str \
        --include-gpus 7 \
        --tag "rps=0.01" \
        --max-containers 5 \
        --generate-report \
        --save-dir $WEBARENA_PLOT_HOME/resource $args

    python resource.py \
        --prometheus-url http://localhost:9090 \
        --start-time "2025-07-26 07:09:11" \
        --end-time "2025-07-26 08:39:27" \
        --data-dir $WEBARENA_PLOT_HOME/.cache/prometheus \
        --include-containers "webarena" \
        --exclude-containers $excluded_containers_str \
        --include-gpus 7 \
        --tag "rps=0.02" \
        --max-containers 5 \
        --generate-report \
        --save-dir $WEBARENA_PLOT_HOME/resource $args

    python resource.py \
        --prometheus-url http://localhost:9090 \
        --start-time "2025-07-28 07:37:40" \
        --end-time "2025-07-28 08:31:31" \
        --data-dir $WEBARENA_PLOT_HOME/.cache/prometheus \
        --include-containers "webarena" \
        --exclude-containers $excluded_containers_str \
        --include-gpus 7 \
        --tag "rps=0.05" \
        --max-containers 5 \
        --generate-report \
        --save-dir $WEBARENA_PLOT_HOME/resource $args

    python resource.py \
        --prometheus-url http://localhost:9090 \
        --start-time "2025-07-24 02:42:18" \
        --end-time "2025-07-24 03:23:26" \
        --data-dir $WEBARENA_PLOT_HOME/.cache/prometheus \
        --include-containers "webarena" "shopping" \
        --exclude-containers $excluded_containers_str \
        --include-gpus 7 \
        --tag "rps=0.1" \
        --max-containers 5 \
        --generate-report \
        --save-dir $WEBARENA_PLOT_HOME/resource $args

    python resource.py \
        --prometheus-url http://localhost:9090 \
        --start-time "2025-07-30 03:39:33" \
        --end-time "2025-07-30 04:25:06" \
        --data-dir $WEBARENA_PLOT_HOME/.cache/prometheus \
        --include-containers "webarena" "shopping" \
        --exclude-containers $excluded_containers_str \
        --include-gpus 7 \
        --tag "rps=0.2" \
        --max-containers 8 \
        --generate-report \
        --save-dir $WEBARENA_PLOT_HOME/resource $args
}

# run_webarena_io
# run_webarena_delay
# run_webarena_rps_delay
# run_webarena_resource

