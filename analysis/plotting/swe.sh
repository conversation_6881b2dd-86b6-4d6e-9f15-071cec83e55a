args="$@"
if [ -z "$args" ]; then
    echo "No additional arguments provided. $args"
else
    echo "Running delay analysis with arguments: $args"
fi

SWE_DATA_HOME=/mnt/data/agbench/results/swe/swe-default
SWE_PLOT_HOME=./plots/swe


run_swe_io(){
    python llm_io.py --tag swe \
        --vllm_metrics_dirs $SWE_DATA_HOME/stage-1/vllm-llm/vllm_request_metrics \
        --agent_metrics_files $SWE_DATA_HOME/stage-1/data/swe-agent/model/e2e_delay.csv \
        --save_dir $SWE_PLOT_HOME/llm_io
        
    python tool_io.py --tag swe \
        --module_dirs $SWE_DATA_HOME/stage-1/data/intermediate/env_manager/requests \
        $SWE_DATA_HOME/stage-1/data/intermediate/server/requests \
        --protocal json_str \
        --log_scale \
        --save_dir $SWE_PLOT_HOME/tool_io
}


run_swe_delay(){
    exps=(
        "stage-2-nolimit"
        "stage-4-nolimit/2threads/rps2"
        "stage-4-nolimit/4threads/rps4"
        "stage-4-nolimit/8threads/rps8"
        "stage-4-nolimit/32threads/rps8"
    )
    for exp in "${exps[@]}"; do
        echo "Processing experiment: $exp"
        data_home="$SWE_DATA_HOME/$exp"
        save_home="$SWE_PLOT_HOME/delay/$exp"
        
        # WebArena Delay Analysis Script
        python delay.py --tag swe \
            --instance_dirs $data_home/data/swe-agent/client \
                            $data_home/data/swe-agent/model \
                            $data_home/data/swe-agent/server \
                            $data_home/data/swe-agent/SWEEnv \
            --exclude_tasks swe_agent_request \
            --include_route \
            --include_delay_types e2e \
            --save_dir $save_home/swe/original $args
        
        python delay.py --tag swe \
            --instance_dirs $data_home/data/swe-agent/client \
                            $data_home/data/swe-agent/model \
                            $data_home/data/swe-agent/server \
                            $data_home/data/swe-agent/SWEEnv \
            --exclude_tasks swe_agent_request \
            --include_route \
            --include_delay_types e2e \
            --merge_task \
            --save_dir $save_home/swe/merged $args
        
        python delay.py --tag swe-e2e \
            --instance_dirs $data_home/data/swe-agent/client \
                            $data_home/data/swe-agent/model \
                            $data_home/data/swe-agent/server \
                            $data_home/data/swe-agent/SWEEnv \
            --exclude_tasks swe_agent_request \
            --include_sources client \
            --include_route \
            --save_dir $save_home/swe-e2e/original $args
        
        python delay.py --tag swe-e2e \
            --instance_dirs $data_home/data/swe-agent/client \
                            $data_home/data/swe-agent/model \
                            $data_home/data/swe-agent/server \
                            $data_home/data/swe-agent/SWEEnv \
            --exclude_tasks swe_agent_request \
            --include_sources client \
        --merge_task \
        --save_dir $save_home/swe-e2e/merged $args
    done
}

run_swe_rps_delay(){
    exps=(
        "stage-2-nolimit"
        "stage-4-nolimit/2threads/rps2"
        "stage-4-nolimit/4threads/rps4"
        "stage-4-nolimit/8threads/rps8"
        "stage-4-nolimit/32threads/rps8"
    )
    instance_dirs=()
    instance_groups=()
    for exp in "${exps[@]}"; do
        echo "Processing experiment: $exp"
        data_home="$SWE_DATA_HOME/$exp"
        save_home="$SWE_PLOT_HOME/multi_delay/$exp"
        
        # Collect instance directories and groups
        instance_dirs+=("$data_home/data/swe-agent/client")
        instance_dirs+=("$data_home/data/swe-agent/model")
        instance_dirs+=("$data_home/data/swe-agent/server")
        instance_dirs+=("$data_home/data/swe-agent/SWEEnv")
        
        # if exp is stage-2-nolimit, add "bsl" to instance_groups
        if [[ "$exp" == "stage-2-nolimit" ]]; then
            instance_groups+=("bsl")
        else
            # otherwise, add "nthreads" with threads value and "rps" with the rps value extracted from the exp string
            threads=$(echo "$exp" | grep -oP '(?<=/)[0-9]+(?=threads)')
            rps=$(echo "$exp" | grep -oP '(?<=rps)[0-9]+')
            instance_groups+=("nthreads=$threads;rps=$rps")
        fi
    done
    # concatenate instance directories into a single string
    instance_dirs_str=$(printf " %s" "${instance_dirs[@]}")
    # concatenate instance groups into a single string
    instance_groups_str=$(printf " %s" "${instance_groups[@]}")
    
    python delay.py --tag swe \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --exclude_tasks swe_agent_request \
        --include_route \
        --include_delay_types e2e \
        --save_dir $SWE_PLOT_HOME/multi_delay/swe/original $args
    
    python delay.py --tag swe \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --exclude_tasks swe_agent_request \
        --include_route \
        --include_delay_types e2e \
        --merge_task \
        --save_dir $SWE_PLOT_HOME/multi_delay/swe/merged $args
    
    python delay.py --tag swe-e2e \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --include_sources client \
        --include_route \
        --save_dir $SWE_PLOT_HOME/multi_delay/swe-e2e/original $args
    
    python delay.py --tag swe-e2e \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --include_sources client \
        --include_route \
        --merge_task \
        --save_dir $SWE_PLOT_HOME/multi_delay/swe-e2e/merged $args
}

run_old_swe_resource(){
    excluded_containers=(
        "cadvisor" "grafana" "prometheus"
        "hgx" "li_triton"
        "fred-pytorch-container"
        "lm-eval" "longcontext-LLaDA"
        "peaceful_matsumoto"
    )
    excluded_containers_str=$(printf " %s" "${excluded_containers[@]}")

    python resource.py \
    --prometheus-url http://localhost:9090 \
    --start-time "2025-07-30 18:19:41" \
    --end-time "2025-07-31 02:07:31" \
    --data-dir $SWE_PLOT_HOME/.cache/prometheus \
    --include-containers "agbench" \
    --exclude-containers $excluded_containers_str \
    --aggregate-containers "sweagent-swe-agent-latest-agbench-one-by-one" \
    --include-gpus 1 \
    --tag test \
    --max-containers 5 \
    --generate-report \
    --save-dir $SWE_PLOT_HOME/resource $args

    python resource.py \
    --prometheus-url http://localhost:9090 \
    --start-time "2025-06-07 09:05:14" \
    --end-time "2025-06-11 09:24:28" \
    --data-dir $SWE_PLOT_HOME/.cache/prometheus \
    --include-containers "swe" "agbench" \
    --exclude-containers $excluded_containers_str \
    --aggregate-containers "sweagent-swe-agent-latest" \
    --include-gpus 7 \
    --tag bsl \
    --max-containers 5 \
    --generate-report \
    --save-dir $SWE_PLOT_HOME/resource $args
    
    python resource.py \
    --prometheus-url http://localhost:9090 \
    --start-time "2025-06-07 09:05:14" \
    --end-time "2025-06-07 11:05:14" \
    --data-dir $SWE_PLOT_HOME/.cache/prometheus \
    --include-containers "swe" "agbench" \
    --exclude-containers $excluded_containers_str \
    --aggregate-containers "sweagent-swe-agent-latest" \
    --include-gpus 7 \
    --tag bsl-0h-2h \
    --max-containers 5 \
    --generate-report \
    --save-dir $SWE_PLOT_HOME/resource $args

    python resource.py \
    --prometheus-url http://localhost:9090 \
    --start-time "2025-06-09 09:05:14" \
    --end-time "2025-06-09 11:05:14" \
    --data-dir $SWE_PLOT_HOME/.cache/prometheus \
    --include-containers "swe" "agbench" \
    --exclude-containers $excluded_containers_str \
    --aggregate-containers "sweagent-swe-agent-latest" \
    --include-gpus 7 \
    --tag bsl-48h-2h \
    --max-containers 5 \
    --generate-report \
    --save-dir $SWE_PLOT_HOME/resource $args

    python resource.py \
    --prometheus-url http://localhost:9090 \
    --start-time "2025-06-18 18:00:38" \
    --end-time "2025-06-20 03:43:29" \
    --data-dir $SWE_PLOT_HOME/.cache/prometheus \
    --include-containers "swe" "agbench" \
    --exclude-containers $excluded_containers_str \
    --aggregate-containers "sweagent-swe-agent-latest" \
    --include-gpus 7 \
    --tag "nthread=2;rps=2" \
    --max-containers 5 \
    --generate-report \
    --save-dir $SWE_PLOT_HOME/resource $args
    
    python resource.py \
    --prometheus-url http://localhost:9090 \
    --start-time "2025-06-14 15:23:23" \
    --end-time "2025-06-15 19:51:23" \
    --data-dir $SWE_PLOT_HOME/.cache/prometheus \
    --include-containers "swe" "agbench" \
    --exclude-containers $excluded_containers_str \
    --aggregate-containers "sweagent-swe-agent-latest" \
    --include-gpus 7 \
    --tag "nthread=4;rps=4" \
    --max-containers 5 \
    --generate-report \
    --save-dir $SWE_PLOT_HOME/resource $args
    
    python resource.py \
    --prometheus-url http://localhost:9090 \
    --start-time "2025-06-16 06:56:31" \
    --end-time "2025-06-17 07:54:24" \
    --data-dir $SWE_PLOT_HOME/.cache/prometheus \
    --include-containers "swe" "agbench" \
    --exclude-containers $excluded_containers_str \
    --aggregate-containers "sweagent-swe-agent-latest" \
    --include-gpus 7 \
    --tag "nthread=8;rps=8" \
    --max-containers 5 \
    --generate-report \
    --save-dir $SWE_PLOT_HOME/resource $args
    
    python resource.py \
    --prometheus-url http://localhost:9090 \
    --start-time "2025-06-12 23:49:00" \
    --end-time "2025-06-14 13:19:57" \
    --data-dir $SWE_PLOT_HOME/.cache/prometheus \
    --include-containers "swe" "agbench" \
    --exclude-containers $excluded_containers_str \
    --aggregate-containers "sweagent-swe-agent-latest" \
    --include-gpus 7 \
    --tag "nthread=32;rps=8" \
    --max-containers 5 \
    --generate-report \
    --save-dir $SWE_PLOT_HOME/resource $args
}

# run_swe_io
# run_swe_delay
# run_swe_rps_delay
# run_old_swe_resource


run_swe_resource(){
    excluded_containers=(
        "cadvisor" "grafana" "prometheus"
        "hgx" "li_triton"
        "fred-pytorch-container"
        "lm-eval" "longcontext-LLaDA"
        "peaceful_matsumoto"
        "s1-nolimit" "s3-model-module"
        "devstral-s1" "default-s1"
    )
    excluded_containers_str=$(printf " %s" "${excluded_containers[@]}")

    # Too Long, Never RUN it.
    # python resource.py \
    # --prometheus-url http://localhost:9090 \
    # --start-time "2025-07-18 04:13:19" \
    # --end-time "2025-07-25 08:09:06" \
    # --data-dir $SWE_PLOT_HOME/.cache/prometheus \
    # --include-containers "swe" "agbench" \
    # --exclude-containers $excluded_containers_str \
    # --aggregate-containers "sweagent-swe-agent-latest" \
    # --include-gpus 7 \
    # --tag bsl \
    # --max-containers 5 \
    # --generate-report \
    # --save-dir $SWE_PLOT_HOME/resource $args
    
    python resource.py \
    --prometheus-url http://localhost:9090 \
    --start-time "2025-07-18 04:13:19" \
    --end-time "2025-07-18 05:13:19" \
    --data-dir $SWE_PLOT_HOME/.cache/prometheus \
    --include-containers "swe" "agbench" \
    --exclude-containers $excluded_containers_str \
    --include-gpus 1 2 \
    --aggregate-containers "sweagent-swe-agent-latest" \
    --tag bsl-0h-1h \
    --max-containers 5 \
    --generate-report \
    --save-dir $SWE_PLOT_HOME/resource $args

    python resource.py \
    --prometheus-url http://localhost:9090 \
    --start-time "2025-07-19 04:13:19" \
    --end-time "2025-07-19 05:13:19" \
    --data-dir $SWE_PLOT_HOME/.cache/prometheus \
    --include-containers "swe" "agbench" \
    --exclude-containers $excluded_containers_str \
    --include-gpus 1 2 \
    --aggregate-containers "sweagent-swe-agent-latest" \
    --tag bsl-1d-1h \
    --max-containers 5 \
    --generate-report \
    --save-dir $SWE_PLOT_HOME/resource $args

    python resource.py \
    --prometheus-url http://localhost:9090 \
    --start-time "2025-07-20 04:13:19" \
    --end-time "2025-07-20 05:13:19" \
    --data-dir $SWE_PLOT_HOME/.cache/prometheus \
    --include-containers "swe" "agbench" \
    --exclude-containers $excluded_containers_str \
    --include-gpus 1 2 \
    --aggregate-containers "sweagent-swe-agent-latest" \
    --tag bsl-2d-1h \
    --max-containers 5 \
    --generate-report \
    --save-dir $SWE_PLOT_HOME/resource $args

    python resource.py \
    --prometheus-url http://localhost:9090 \
    --start-time "2025-07-25 04:13:19" \
    --end-time "2025-07-25 05:13:19" \
    --data-dir $SWE_PLOT_HOME/.cache/prometheus \
    --include-containers "swe" "agbench" \
    --exclude-containers $excluded_containers_str \
    --include-gpus 1 2 \
    --aggregate-containers "sweagent-swe-agent-latest" \
    --tag bsl-7d-1h \
    --max-containers 5 \
    --generate-report \
    --save-dir $SWE_PLOT_HOME/resource $args
}

# run_swe_resource


run_swe_resource_offloading(){
    excluded_containers=(
        "cadvisor" "grafana" "prometheus"
        "hgx" "li_triton"
        "fred-pytorch-container"
        "lm-eval" "longcontext-LLaDA"
        "peaceful_matsumoto"
        "s1-nolimit" "s3-model-module"
        "devstral-s1" "default-s1"
    )
    excluded_containers_str=$(printf " %s" "${excluded_containers[@]}")
    
    # 2025-08-18 16:47:40
    # 2025-08-19 01:41:35
    # 2025-08-19 02:06:26
    # 2025-08-19 07:00:07

    # python resource.py \
    # --prometheus-url http://localhost:9090 \
    # --start-time "2025-08-18 16:47:40" \
    # --end-time "2025-08-19 01:41:35" \
    # --data-dir $SWE_PLOT_HOME/.cache/prometheus \
    # --include-containers "swe" "sweagent-swe-agent-latest" \
    # --exclude-containers $excluded_containers_str \
    # --include-gpus 7 \
    # --aggregate-containers "sweagent-swe-agent-latest" \
    # --tag non-offloading \
    # --max-containers 10 \
    # --generate-report \
    # --save-dir $SWE_PLOT_HOME/resource $args
    
    # python resource.py \
    # --prometheus-url http://localhost:9090 \
    # --start-time "2025-08-19 02:06:26" \
    # --end-time "2025-08-19 07:00:07" \
    # --data-dir $SWE_PLOT_HOME/.cache/prometheus \
    # --include-containers "swe" "sweagent-swe-agent-latest" \
    # --exclude-containers $excluded_containers_str \
    # --include-gpus 7 \
    # --aggregate-containers "sweagent-swe-agent-latest" \
    # --tag offloading \
    # --max-containers 10 \
    # --generate-report \
    # --save-dir $SWE_PLOT_HOME/resource $args
    
    python resource.py \
    --prometheus-url http://localhost:9090 \
    --start-time "2025-08-18 16:47:40" \
    --end-time "2025-08-18 20:47:40" \
    --data-dir $SWE_PLOT_HOME/.cache/prometheus \
    --include-containers "swe" "sweagent-swe-agent-latest" \
    --exclude-containers $excluded_containers_str \
    --include-gpus 7 \
    --aggregate-containers "sweagent-swe-agent-latest" \
    --tag non-offloading-4h \
    --max-containers 10 \
    --generate-report \
    --save-dir $SWE_PLOT_HOME/resource $args
    
    python resource.py \
    --prometheus-url http://localhost:9090 \
    --start-time "2025-08-19 02:06:26" \
    --end-time "2025-08-19 06:06:26" \
    --data-dir $SWE_PLOT_HOME/.cache/prometheus \
    --include-containers "swe" "sweagent-swe-agent-latest" \
    --exclude-containers $excluded_containers_str \
    --include-gpus 7 \
    --aggregate-containers "sweagent-swe-agent-latest" \
    --tag offloading-4h \
    --max-containers 10 \
    --generate-report \
    --save-dir $SWE_PLOT_HOME/resource $args
}

run_swe_resource_offloading