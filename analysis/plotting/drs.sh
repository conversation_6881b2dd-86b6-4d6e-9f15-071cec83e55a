args="$@"
if [ -z "$args" ]; then
    echo "No additional arguments provided. $args"
else
    echo "Running delay analysis with arguments: $args"
fi

DRS_DATA_HOME=/mnt/data/agbench/results/drs/final/baseline
DRS_PLOT_HOME=./plots/drs


run_drs_io(){
    python llm_io.py --tag drs \
        --vllm_metrics_dirs $DRS_DATA_HOME/stage-1/vllm-llm/vllm_request_metrics \
        --agent_metrics_files $DRS_DATA_HOME/stage-1/data/drs-agent/model/e2e_delay.csv \
        --save_dir $DRS_PLOT_HOME/llm_io
        
    python tool_io.py --tag drs \
        --module_dirs $DRS_DATA_HOME/stage-1/data/intermediate/env_manager/requests \
        $DRS_DATA_HOME/stage-1/data/intermediate/server/requests \
        --protocal json_str \
        --log_scale \
        --save_dir $DRS_PLOT_HOME/tool_io
}


run_drs_delay(){
    exps=(
        "stage-2-nolimit"
        "stage-4-nolimit/2threads/rps2"
        "stage-4-nolimit/4threads/rps4"
        "stage-4-nolimit/8threads/rps8"
        "stage-4-nolimit/32threads/rps8"
    )
    for exp in "${exps[@]}"; do
        echo "Processing experiment: $exp"
        data_home="$DRS_DATA_HOME/$exp"
        save_home="$DRS_PLOT_HOME/delay/$exp"
        
        # WebArena Delay Analysis Script
        python delay.py --tag drs \
            --instance_dirs $data_home/data/drs-agent/client \
                            $data_home/data/drs-agent/model \
                            $data_home/data/drs-agent/server \
                            $data_home/data/drs-agent/SWEEnv \
            --exclude_tasks drs_agent_request \
            --include_route \
            --include_delay_types e2e \
            --save_dir $save_home/drs/original $args
        
        python delay.py --tag drs \
            --instance_dirs $data_home/data/drs-agent/client \
                            $data_home/data/drs-agent/model \
                            $data_home/data/drs-agent/server \
                            $data_home/data/drs-agent/SWEEnv \
            --exclude_tasks drs_agent_request \
            --include_route \
            --include_delay_types e2e \
            --merge_task \
            --save_dir $save_home/drs/merged $args
        
        python delay.py --tag drs-e2e \
            --instance_dirs $data_home/data/drs-agent/client \
                            $data_home/data/drs-agent/model \
                            $data_home/data/drs-agent/server \
                            $data_home/data/drs-agent/SWEEnv \
            --exclude_tasks drs_agent_request \
            --include_sources client \
            --include_route \
            --save_dir $save_home/drs-e2e/original $args
        
        python delay.py --tag drs-e2e \
            --instance_dirs $data_home/data/drs-agent/client \
                            $data_home/data/drs-agent/model \
                            $data_home/data/drs-agent/server \
                            $data_home/data/drs-agent/SWEEnv \
            --exclude_tasks drs_agent_request \
            --include_sources client \
        --merge_task \
        --save_dir $save_home/drs-e2e/merged $args
    done
}

run_drs_rps_delay(){
    exps=(
        "stage-2-nolimit"
        "stage-4-nolimit/2threads/rps2"
        "stage-4-nolimit/4threads/rps4"
        "stage-4-nolimit/8threads/rps8"
        "stage-4-nolimit/32threads/rps8"
    )
    instance_dirs=()
    instance_groups=()
    for exp in "${exps[@]}"; do
        echo "Processing experiment: $exp"
        data_home="$DRS_DATA_HOME/$exp"
        save_home="$DRS_PLOT_HOME/multi_delay/$exp"
        
        # Collect instance directories and groups
        instance_dirs+=("$data_home/data/drs-agent/client")
        instance_dirs+=("$data_home/data/drs-agent/model")
        instance_dirs+=("$data_home/data/drs-agent/server")
        instance_dirs+=("$data_home/data/drs-agent/SWEEnv")
        
        # if exp is stage-2-nolimit, add "bsl" to instance_groups
        if [[ "$exp" == "stage-2-nolimit" ]]; then
            instance_groups+=("bsl")
        else
            # otherwise, add "nthreads" with threads value and "rps" with the rps value extracted from the exp string
            threads=$(echo "$exp" | grep -oP '(?<=/)[0-9]+(?=threads)')
            rps=$(echo "$exp" | grep -oP '(?<=rps)[0-9]+')
            instance_groups+=("nthreads=$threads;rps=$rps")
        fi
    done
    # concatenate instance directories into a single string
    instance_dirs_str=$(printf " %s" "${instance_dirs[@]}")
    # concatenate instance groups into a single string
    instance_groups_str=$(printf " %s" "${instance_groups[@]}")
    
    python delay.py --tag drs \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --exclude_tasks drs_agent_request \
        --include_route \
        --include_delay_types e2e \
        --save_dir $DRS_PLOT_HOME/multi_delay/drs/original $args
    
    python delay.py --tag drs \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --exclude_tasks drs_agent_request \
        --include_route \
        --include_delay_types e2e \
        --merge_task \
        --save_dir $DRS_PLOT_HOME/multi_delay/drs/merged $args
    
    python delay.py --tag drs-e2e \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --include_sources client \
        --include_route \
        --save_dir $DRS_PLOT_HOME/multi_delay/drs-e2e/original $args
    
    python delay.py --tag drs-e2e \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --include_sources client \
        --include_route \
        --merge_task \
        --save_dir $DRS_PLOT_HOME/multi_delay/drs-e2e/merged $args
}

run_drs_resource(){
    excluded_containers=(
        "cadvisor" "grafana" "prometheus"
        "hgx" "li_triton"
        "fred-pytorch-container"
        "lm-eval" "longcontext-LLaDA"
        "peaceful_matsumoto"
    )
    excluded_containers_str=$(printf " %s" "${excluded_containers[@]}")
    aggregated_containers=(
        "drsagent-drs-agent-latest"
    )
    aggregated_containers_str=$(printf " %s" "${aggregated_containers[@]}")

    # python resource.py \
    # --prometheus-url http://localhost:9090 \
    # --start-time "2025-07-08 17:36:14" \
    # --end-time "2025-07-08 18:36:14" \
    # --data-dir $DRS_PLOT_HOME/.cache/prometheus \
    # --include-containers "agbench-lego_agent-drs" \
    # --exclude-containers $excluded_containers_str \
    # --aggregate-containers $aggregated_containers_str \
    # --include-gpus 0 1 2 3 \
    # --tag test \
    # --max-containers 8 \
    # --generate-report \
    # --save-dir $DRS_PLOT_HOME/resource $args

    # python resource.py \
    # --prometheus-url http://localhost:9090 \
    # --start-time "2025-07-08 17:36:14" \
    # --end-time "2025-07-08 23:29:07" \
    # --data-dir $DRS_PLOT_HOME/.cache/prometheus \
    # --include-containers "agbench-lego_agent-drs" \
    # --exclude-containers $excluded_containers_str \
    # --aggregate-containers $aggregated_containers_str \
    # --include-gpus 0 1 2 3 \
    # --tag stage1 \
    # --max-containers 8 \
    # --generate-report \
    # --save-dir $DRS_PLOT_HOME/resource $args

    python resource.py \
    --prometheus-url http://localhost:9090 \
    --start-time "2025-07-09 05:00:00" \
    --end-time "2025-07-09 06:00:00" \
    --data-dir $DRS_PLOT_HOME/.cache/prometheus \
    --include-containers "agbench-lego_agent-drs" \
    --exclude-containers $excluded_containers_str \
    --aggregate-containers $aggregated_containers_str \
    --include-gpus 0 1 2 3 \
    --tag bsl-1h \
    --max-containers 8 \
    --generate-report \
    --save-dir $DRS_PLOT_HOME/resource $args

    # python resource.py \
    # --prometheus-url http://localhost:9090 \
    # --start-time "2025-07-09 03:39:05" \
    # --end-time "2025-07-09 08:57:19" \
    # --data-dir $DRS_PLOT_HOME/.cache/prometheus \
    # --include-containers "agbench-lego_agent-drs" \
    # --exclude-containers $excluded_containers_str \
    # --aggregate-containers $aggregated_containers_str \
    # --include-gpus 0 1 2 3 \
    # --tag bsl \
    # --max-containers 8 \
    # --generate-report \
    # --save-dir $DRS_PLOT_HOME/resource $args

    # python resource.py \
    # --prometheus-url http://localhost:9090 \
    # --start-time "2025-07-16 09:43:45" \
    # --end-time "2025-07-16 10:51:15" \
    # --data-dir $DRS_PLOT_HOME/.cache/prometheus \
    # --include-containers "agbench-lego_agent-drs" \
    # --exclude-containers $excluded_containers_str \
    # --aggregate-containers $aggregated_containers_str \
    # --include-gpus 1 2 3 4 \
    # --tag "nthreads=32;rps=32" \
    # --max-containers 8 \
    # --generate-report \
    # --save-dir $DRS_PLOT_HOME/resource $args

    # python resource.py \
    # --prometheus-url http://localhost:9090 \
    # --start-time "2025-07-13 04:43:08" \
    # --end-time "2025-07-13 05:36:40" \
    # --data-dir $DRS_PLOT_HOME/.cache/prometheus \
    # --include-containers "agbench-lego_agent-drs" \
    # --exclude-containers $excluded_containers_str \
    # --aggregate-containers $aggregated_containers_str \
    # --include-gpus 4 5 6 7 \
    # --tag "nthreads=64;rps=64" \
    # --max-containers 8 \
    # --generate-report \
    # --save-dir $DRS_PLOT_HOME/resource $args
}

# run_drs_io
# run_drs_delay
# run_drs_rps_delay
run_drs_resource

