#!/bin/bash

args="$@"
if [ -z "$args" ]; then
    echo "No additional arguments provided. $args"
else
    echo "Running delay analysis with arguments: $args"
fi

python resource.py \
    --prometheus-url http://localhost:9090 \
    --start-time "2025-07-24 08:11:26" \
    --end-time "2025-07-24 09:12:32" \
    --data-dir ./.cache/data/prometheus/swe-env \
    --tag 512threads \
    --include-containers "sweagent" "agbench" \
    --exclude-containers "env-module-1-threads" "server" "model" "SWEEnv" \
    --aggregate-containers "sweagent-swe-agent" \
    --max-containers 5 \
    --include-gpus 0 1 2 3 4 \
    --exclude-gpus 3 \
    --aggregate-gpus 0,1 2,3 4 \
    --generate-report \
    --save-dir ./.cache/plots $args