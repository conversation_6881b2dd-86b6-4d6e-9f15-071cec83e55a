#!/bin/bash
args="$@"
if [ -z "$args" ]; then
    echo "No additional arguments provided. $args"
else
    echo "Running delay analysis with arguments: $args"
fi

# HuggingGPT
python llm_io.py --tag hugginggpt \
    --vllm_metrics_dirs /mnt/data/agbench/results/hugginggpt/monitor/trace/vllm \
    --agent_metrics_files /mnt/data/agbench/results/hugginggpt/monitor/trace/hugginggpt_server/e2e_delay.csv \
    --save_dir ./plots/llm_io/hgpt $args

# Deep Research
python llm_io.py --tag drs-baseline \
    --vllm_metrics_dirs /mnt/data/agbench/results/drs/final/baseline/stage-1/drs_async-YDCDRS-OneByOne-0.0-202/results/vllm-planner-llm/vllm_request_metrics \
                        /mnt/data/agbench/results/drs/final/baseline/stage-1/drs_async-YDCDRS-OneByOne-0.0-202/results/vllm-writer-llm/vllm_request_metrics \
                        /mnt/data/agbench/results/drs/final/baseline/stage-1/drs_async-YDCDRS-OneByOne-0.0-202/results/vllm-summarization-llm/vllm_request_metrics \
    --agent_metrics_files /mnt/data/agbench/results/drs/final/baseline/stage-1/drs_async-YDCDRS-OneByOne-0.0-202/results/server/monitor/server/e2e_delay.csv \
    --save_dir ./plots/llm_io/drs $args

# SWE Agent
python llm_io.py --tag swe-ds32B \
    --vllm_metrics_dirs /mnt/data/agbench/results/swe/swe-ds32B/stage-1-batchx4/vllm-llm/vllm_request_metrics \
    --agent_metrics_files /mnt/data/agbench/results/swe/swe-ds32B/stage-1-batchx4/data/swe-agent/model/e2e_delay.csv \
    --save_dir ./plots/llm_io/swe-ds32B $args

# python llm_io.py --tag swe \
#     --vllm_metrics_dirs /mnt/data/agbench/results/swe/swe-default/stage-1/vllm-llm/vllm_request_metrics \
#     --agent_metrics_files /mnt/data/agbench/results/swe/swe-default/stage-1/data/swe-agent/model/e2e_delay.csv \
#     --save_dir ./plots/llm_io/swe

# WebArena
python llm_io.py --tag webarena \
    --vllm_metrics_dirs /mnt/data/agbench/results/webarena/monitor/trace/vllm \
    --agent_metrics_files /mnt/data/agbench/results/webarena/monitor/trace/llm/e2e_delay.csv \
    --save_dir ./plots/llm_io/webarena $args

# MoA
python llm_io.py --tag moa \
    --vllm_metrics_dirs /mnt/data/agbench/results/moa/monitor/trace/vllm \
    --agent_metrics_files /mnt/data/agbench/results/moa/monitor/trace/moa_service/e2e_delay.csv \
    --save_dir ./plots/llm_io/moa $args

# RAG Agents
python llm_io.py --tag naive-rag-wqa \
    --vllm_metrics_dirs /mnt/data/agbench/results/rag-v0/naive_rag/stage-1/naive-WQA-OneByOne-0.0-10000/results/vllm-llm/vllm_request_metrics \
    --agent_metrics_files /mnt/data/agbench/results/rag-v0/naive_rag/stage-1/naive-WQA-OneByOne-0.0-10000/results/server/monitor/rag_server/e2e_delay.csv \
    --save_dir ./plots/llm_io/naive_rag $args

python llm_io.py --tag advanced-rag-wqa \
    --vllm_metrics_dirs /mnt/data/agbench/results/rag-v0/advanced_rag/stage-1/advanced-WQA-OneByOne-0.0-10000/results/vllm-llm/vllm_request_metrics \
    --agent_metrics_files /mnt/data/agbench/results/rag-v0/advanced_rag/stage-1/advanced-WQA-OneByOne-0.0-10000/results/server/monitor/rag_server/e2e_delay.csv \
    --save_dir ./plots/llm_io/advanced_rag $args

python llm_io.py --tag dynamic-rag-wqa \
    --vllm_metrics_dirs /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-1/dynamic-WQA-OneByOne-0.0-10000/results/vllm-llm/vllm_request_metrics \
    --agent_metrics_files /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-1/dynamic-WQA-OneByOne-0.0-10000/results/server/monitor/rag_server/e2e_delay.csv \
    --save_dir ./plots/llm_io/dynamic_rag $args

python llm_io.py --tag dynamic-rag-wqa-h20 \
    --vllm_metrics_dirs /mnt/data/agbench/results/rag/dynamic_rag/stage-1/dynamic-WQA-OneByOne-0.0-10000/results/vllm-llm/vllm_request_metrics \
    --agent_metrics_files /mnt/data/agbench/results/rag/dynamic_rag/stage-1/dynamic-WQA-OneByOne-0.0-10000/results/server/monitor/rag_server/e2e_delay.csv \
    --save_dir ./plots/llm_io/dynamic_rag --plots predictability


# python llm_io.py --tag agents \
#     --vllm_metrics_dirs /mnt/data/agbench/results/hugginggpt/monitor/trace/vllm \
#                         /mnt/data/agbench/results/drs/final/baseline/stage-1/drs_async-YDCDRS-OneByOne-0.0-202/results/vllm-planner-llm/vllm_request_metrics \
#                         /mnt/data/agbench/results/drs/final/baseline/stage-1/drs_async-YDCDRS-OneByOne-0.0-202/results/vllm-writer-llm/vllm_request_metrics \
#                         /mnt/data/agbench/results/drs/final/baseline/stage-1/drs_async-YDCDRS-OneByOne-0.0-202/results/vllm-summarization-llm/vllm_request_metrics \
#                         /mnt/data/agbench/results/swe/swe-ds32B/stage-1-batchx4/vllm-llm/vllm_request_metrics \
#                         /mnt/data/agbench/results/webarena/monitor/trace/vllm \
#                         /mnt/data/agbench/results/moa/monitor/trace/vllm \
#                         /mnt/data/agbench/results/rag-v0/naive_rag/stage-1/naive-WQA-OneByOne-0.0-10000/results/vllm-llm/vllm_request_metrics \
#                         /mnt/data/agbench/results/rag-v0/advanced_rag/stage-1/advanced-WQA-OneByOne-0.0-10000/results/vllm-llm/vllm_request_metrics \
#                         /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-1/dynamic-WQA-OneByOne-0.0-10000/results/vllm-llm/vllm_request_metrics \
#     --agent_metrics_files   /mnt/data/agbench/results/hugginggpt/monitor/trace/hugginggpt_server/e2e_delay.csv \
#                             /mnt/data/agbench/results/drs/final/baseline/stage-1/drs_async-YDCDRS-OneByOne-0.0-202/results/server/monitor/server/e2e_delay.csv \
#                             /mnt/data/agbench/results/swe/swe-ds32B/stage-1-batchx4/data/swe-agent/model/e2e_delay.csv \
#                             /mnt/data/agbench/results/webarena/monitor/trace/llm/e2e_delay.csv \
#                             /mnt/data/agbench/results/moa/monitor/trace/moa_service/e2e_delay.csv \
#                             /mnt/data/agbench/results/rag-v0/naive_rag/stage-1/naive-WQA-OneByOne-0.0-10000/results/server/monitor/rag_server/e2e_delay.csv \
#                             /mnt/data/agbench/results/rag-v0/advanced_rag/stage-1/advanced-WQA-OneByOne-0.0-10000/results/server/monitor/rag_server/e2e_delay.csv \
#                             /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-1/dynamic-WQA-OneByOne-0.0-10000/results/server/monitor/rag_server/e2e_delay.csv \
#     --save_dir ./plots/llm_io


# python llm_io.py --tag agents \
#     --vllm_metrics_dirs /mnt/data/agbench/results/hugginggpt/monitor/trace/vllm \
#                         /mnt/data/agbench/results/drs/final/baseline/stage-1/drs_async-YDCDRS-OneByOne-0.0-202/results/vllm-planner-llm/vllm_request_metrics \
#                         /mnt/data/agbench/results/drs/final/baseline/stage-1/drs_async-YDCDRS-OneByOne-0.0-202/results/vllm-writer-llm/vllm_request_metrics \
#                         /mnt/data/agbench/results/drs/final/baseline/stage-1/drs_async-YDCDRS-OneByOne-0.0-202/results/vllm-summarization-llm/vllm_request_metrics \
#                         /mnt/data/agbench/results/swe/swe-ds32B/stage-1-batchx4/vllm-llm/vllm_request_metrics \
#                         /mnt/data/agbench/results/webarena/monitor/trace/vllm \
#                         /mnt/data/agbench/results/moa/monitor/trace/vllm \
#                         /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-1/dynamic-WQA-OneByOne-0.0-10000/results/vllm-llm/vllm_request_metrics \
#     --agent_metrics_files   /mnt/data/agbench/results/hugginggpt/monitor/trace/hugginggpt_server/e2e_delay.csv \
#                             /mnt/data/agbench/results/drs/final/baseline/stage-1/drs_async-YDCDRS-OneByOne-0.0-202/results/server/monitor/server/e2e_delay.csv \
#                             /mnt/data/agbench/results/swe/swe-ds32B/stage-1-batchx4/data/swe-agent/model/e2e_delay.csv \
#                             /mnt/data/agbench/results/webarena/monitor/trace/llm/e2e_delay.csv \
#                             /mnt/data/agbench/results/moa/monitor/trace/moa_service/e2e_delay.csv \
#                             /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-1/dynamic-WQA-OneByOne-0.0-10000/results/server/monitor/rag_server/e2e_delay.csv \
#     --save_dir ./plots/llm_io
