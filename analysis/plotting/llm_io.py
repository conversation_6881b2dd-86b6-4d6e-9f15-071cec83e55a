import os, sys
import pandas as pd
from pandas import DataFrame
from typing import Optional, Dict
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from tqdm import tqdm
from difflib import SequenceMatcher
from typing import List, Tuple
from sklearn.linear_model import LinearRegression

module_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
print(f"Adding module path: {module_path}")
if module_path not in sys.path:
    print(f"Appending {module_path} to sys.path")
    sys.path.append(module_path)

from llm.io_data import parse_llm_call_metrics
from plotting.utils import shorten_task_name


def plot_prompt_gen_length_dist(
    data: DataFrame,
    tag: str = "test_agent",
    save_dir: str = "./plots",
    **kwargs: Dict[str, str],
) -> str:
    """Plot a scatter/hist2d/hexbin plot of (prompt_length, gen_length) for all llm tasks used by an agent
    Args:
            data: pd.DataFrame with (at least) columns [task_name: str, prompt_length: int, generation_length: int]
            tag: tag of the plot, which could be the name of an agent.
            save_dir: directory to save the plot.
    Return:
            fig_path: path to the figure.
    """
    # Handle column name variations
    prompt_col = "prompt_length"
    gen_col = "generation_length"
    plot_type = kwargs.get("plot_type", "scatter")

    # Check for alternative column names
    if prompt_col not in data.columns:
        raise ValueError(f"Column '{prompt_col}' not found in data")
    if gen_col not in data.columns:
        raise ValueError(f"Column '{gen_col}' not found in data")

    # Create the plot
    plt.figure(figsize=(10, 8))

    # Get unique task names for different colors
    data = shorten_task_name(data)
    task_names = data["task_name"].unique()
    task_names = sorted(task_names)  # Sort task names for consistent ordering
    colors = plt.cm.tab10(np.linspace(0, 1, len(task_names)))
    cmaps_for_tasks = [
        "Reds",
        "Blues",
        "Greens",
        "Purples",
        "Oranges",
        "Greys",
        "YlOrBr",
        "YlGnBu",
        "YlGn",
        "OrRd",
        "PuBuGn",
        "BuPu",
        "RdPu",
        "PuRd",
        "Oranges_r",
    ]
    import plotly.express as px

    # Create scatter plot for each task
    for i, task in enumerate(task_names):
        task_data = data[data["task_name"] == task]
        if plot_type == "scatter":
            plt.scatter(
                task_data[prompt_col],
                task_data[gen_col],
                alpha=0.6,
                label=task,
                color=colors[i],
            )
        elif plot_type == "hist2d":
            plt.hist2d(
                task_data[prompt_col],
                task_data[gen_col],
                bins=50,
                # cmap="Blues",
                cmap=cmaps_for_tasks[i],
                alpha=0.6,
                label=task,
            )
        elif plot_type == "hexbin":
            plt.hexbin(
                task_data[prompt_col],
                task_data[gen_col],
                gridsize=50,
                # cmap="Blues",
                cmap=cmaps_for_tasks[i],
                alpha=0.6,
                label=task,
            )
        else:
            raise ValueError(f"Unsupported plot type: {plot_type}")

    if kwargs.get("log_scale", False):
        plt.xscale("log")
        plt.yscale("log")
        # plt.xlim(left=1)
        # plt.ylim(bottom=1)

    plt.xlabel("Prompt Length (tokens)")
    plt.ylabel("Generation Length (tokens)")
    plt.title(f"Prompt vs Generation Length Distribution - {tag}")
    # plt.legend(bbox_to_anchor=(1.05, 1), loc="upper left")
    plt.legend()
    plt.grid(True, alpha=0.3)

    # Save the figure
    os.makedirs(save_dir, exist_ok=True)
    fig_path = f"{save_dir}/{tag}_prompt_gen_length_dist.png"
    plt.tight_layout()
    plt.savefig(fig_path, dpi=300, bbox_inches="tight")
    plt.close()

    print(f"Saved prompt generation length distribution plot to {fig_path}")
    return fig_path


def plot_prompt_sharing_dist(
    data: DataFrame,
    tag: str = "test_agent",
    save_dir: str = "./plots",
    **kwargs: Dict[str, str],
) -> str:
    """Plot the distribution of the fraction of shared prompt for every llm task use by an agent.
            There will be multiple subplots, one for each llm task
    Args:
            data: pd.DataFrame with (at least) columns [task_name: str, prompt_length: int, shared_prompt_length: int, request_time: float, response_time: float, prompt: str]
            tag: tag of the plot, which could be the name of an agent.
            save_dir: directory to save the plot.
    Return:
            fig_path: path to the figure.
    """
    # Check if required columns exist
    required_cols = [
        "task_name",
        "prompt_length",
        "shared_prompt_length",
        "request_time",
        "response_time",
        "prompt",
    ]
    if not all(col in data.columns for col in required_cols):
        missing_cols = [col for col in required_cols if col not in data.columns]
        raise ValueError(f"Missing required columns: {missing_cols}")

    def _find_shared_prefix(prompt: str, prompts: List[str]) -> int:
        """
        Find the total length of the prompt that shares with previous prompts
        by finding the longest common substring and merging overlapping parts.
        """
        if not prompts or not prompt:
            return 0

        max_size = 0
        for p in prompts:
            if not p:
                continue
            # use os.path.commonprefix to find the longest common prefix
            common_prefix = os.path.commonprefix([prompt, p])
            max_size = max(max_size, len(common_prefix))
        return max_size

    def _calculate_sharing_for_data(group: DataFrame) -> DataFrame:
        """Apply function to calculate sharing for all requests"""
        group = group.sort_values("request_time").reset_index(drop=True)
        shared_lengths = []

        for idx, row in tqdm(
            group.iterrows(), total=len(group), desc="Calculating shared prompts"
        ):
            prompt = str(row["prompt"])
            shared_length = 0
            other_gens = group.loc[
                group["response_time"] <= row["request_time"], "prompt"
            ].tolist()
            # Exclude the generation of the current task if it exists
            if row["prompt"] in other_gens:
                other_gens.remove(row["prompt"])

            shared_length = _find_shared_prefix(prompt, other_gens)
            shared_lengths.append(shared_length)

        group["shared_prompt_length"] = shared_lengths
        return group

    # Main processing logic
    data_copy = data.copy()

    # Ensure prompt column is of string type to prevent errors
    data_copy["prompt"] = data_copy["prompt"].astype(str)

    if "shared_prompt_length" not in data_copy.columns:
        data_copy["shared_prompt_length"] = 0
    # if shared_prompt_length are not all 0, we skip the calculation
    if data_copy["shared_prompt_length"].sum() == 0:
        tqdm.pandas(desc="Processing requests")
        # Apply the efficient calculation to each group
        processed_data = _calculate_sharing_for_data(data_copy)
        processed_data = processed_data.reset_index(drop=True)
        processed_data["prompt_length"] = processed_data["prompt"].str.len()
    else:
        processed_data = data_copy.copy()
        print("Using existing shared_prompt_length column for calculations.")
    # Calculate dependent prompt fraction
    processed_data["shared_prompt_fraction"] = (
        processed_data["shared_prompt_length"] / processed_data["prompt_length"]
    ).fillna(0)

    # Ensure fraction is between 0 and 1
    processed_data["shared_prompt_fraction"].clip(0, 1, inplace=True)

    # compute the break down of shared prompt fraction.
    # shared prompt can be divided into two parts:
    # 1. shared prompt from the same task, which is the prompt that is shared
    #    with the previous requests of the same task.
    # 2. shared prompt from other workflow execution, which is the prompt that is shared
    #    with the previous requests of same tasks but in other workflow execution.
    #    For the same task in the same workflow execution, this value is the same, the value is the shared_prompt of the first request of the task in that workflow execution.
    processed_data["shared_context_prompt_length"] = 0
    processed_data["shared_system_prompt_length"] = 0

    # compute the second type of shared prompt
    for task in processed_data["task_name"].unique():
        task_data = processed_data[processed_data["task_name"] == task]
        # group by request_id, for each group, get the first request
        # of this task
        for request_id, group in task_data.groupby("request_id"):
            if len(group) >= 1:
                # if there are multiple requests for the same request_id,
                # we take the first one in terms of request_time
                first_request = group.sort_values("request_time").iloc[0]
                # get the shared prompt length of the first request
                shared_prompt_length = first_request["shared_prompt_length"].astype(int)
                # assert False, f"{type(shared_prompt_length)}, {shared_prompt_length}"
                # set the shared prompt length for all requests in this group
                processed_data.loc[group.index, "shared_system_prompt_length"] = (
                    shared_prompt_length
                )
            else:
                # if there is only one request, we set it to 0
                processed_data.loc[group.index, "shared_system_prompt_length"] = 0

    # shared_context_prompt_length = shared_prompt_length - shared_system_prompt_length
    processed_data["shared_context_prompt_length"] = (
        processed_data["shared_prompt_length"]
        - processed_data["shared_system_prompt_length"]
    ).fillna(0)

    processed_data["shared_context_prompt_fraction"] = (
        processed_data["shared_context_prompt_length"] / processed_data["prompt_length"]
    ).fillna(0)
    processed_data["shared_system_prompt_fraction"] = (
        processed_data["shared_system_prompt_length"] / processed_data["prompt_length"]
    ).fillna(0)

    # Get unique task names
    data = shorten_task_name(processed_data)
    task_names = data["task_name"].unique()
    task_names = sorted(task_names)  # Sort task names for consistent ordering
    n_tasks = len(task_names)

    # Create subplots
    ncols = 2 if n_tasks > 2 else 1
    if n_tasks >= 6:
        ncols = 3
    if n_tasks >= 12:
        ncols = 4
    nrows = (n_tasks + ncols - 1) // ncols  #
    # fig, axes = plt.subplots(n_tasks, 1, figsize=(10, 4 * n_tasks))
    fig, axes = plt.subplots(nrows, ncols, figsize=(6 * ncols, 4 * nrows))
    # Flatten axes if there are multiple subplots
    if n_tasks > 1 and nrows > 1:
        axes = axes.flatten()
    # If there's only one task, we still want axes to be iterable
    if n_tasks == 1:
        print("Only one task, using single axes.", type(axes))
        axes = [axes]

    for i, task in enumerate(task_names):
        task_data = data[data["task_name"] == task]

        # Create histogram
        axes[i].hist(
            task_data["shared_prompt_fraction"],
            bins=20,
            alpha=0.7,
            edgecolor="black",
            label="Shared Prompt",
        )
        # Add statistics
        mean_frac = task_data["shared_prompt_fraction"].mean()
        axes[i].axvline(
            mean_frac, color="red", linestyle="--", label=f"Mean: {mean_frac:.3f}"
        )

        show_breakdown = kwargs.get("show_breakdown", False)
        if show_breakdown:
            # if not all zero
            if task_data["shared_system_prompt_fraction"].sum() > 0:
                axes[i].hist(
                    task_data["shared_system_prompt_fraction"],
                    bins=20,
                    alpha=0.3,
                    label="Shared System Prompt",
                    edgecolor="black",
                )
                # add axvline for mean of fraction of shared system prompt
                mean_sysprompt_frac = task_data["shared_system_prompt_fraction"].mean()
                axes[i].axvline(
                    mean_sysprompt_frac,
                    color="blue",
                    linestyle="--",
                    label=f"Mean for System Prompt only: {mean_sysprompt_frac:.3f}",
                )

        axes[i].set_xlabel("Shared Prompt Fraction")
        axes[i].set_ylabel("Frequency")
        axes[i].set_title(f"Shared Prompt Distribution - {task}")
        axes[i].grid(True, alpha=0.3)

        axes[i].legend()

    plt.suptitle(f"Shared Prompt Distribution by Task - {tag}", fontsize=16, y=1.02)

    # Save the figure
    os.makedirs(save_dir, exist_ok=True)
    fig_path = f"{save_dir}/{tag}_prompt_sharing_dist.png"
    plt.tight_layout()
    plt.savefig(fig_path, dpi=300, bbox_inches="tight")
    plt.close()

    print(f"Saved prompt sharing distribution plot to {fig_path}")
    return fig_path


def plot_prompt_dependency_dist(
    data: DataFrame,
    tag: str = "test_agent",
    extractors: Optional[Dict[str, callable]] = None,
    save_dir: str = "./plots",
    **kwargs: Dict[str, str],
) -> str:
    """
    Plot the distribution of the fraction of dependent prompt for every llm task used by an agent.

    This optimized version improves performance by using a faster string matching library (difflib)
    and by vectorizing the main data processing loop with pandas' apply method.

    Args:
            data: pd.DataFrame with required columns.
            tag: Tag for the plot, often the agent's name.
            extractors: Optional functions to extract dependent prompts for each task.
            save_dir: Directory to save the plot.
    Return:
            Path to the saved figure.
    """
    required_cols = [
        "request_id",
        "task_name",
        "prompt",
        "generation",
        "request_time",
        "response_time",
    ]
    if not all(col in data.columns for col in required_cols):
        missing_cols = [col for col in required_cols if col not in data.columns]
        raise ValueError(f"Missing required columns: {missing_cols}")

    def _merge_intervals(intervals: List[Tuple[int, int]]) -> int:
        """Merge overlapping intervals and return the sum of their lengths."""
        if not intervals:
            return 0

        # Sort intervals by their start point
        intervals.sort(key=lambda x: x[0])

        merged = [intervals[0]]
        for current_start, current_end in intervals[1:]:
            last_start, last_end = merged[-1]
            if current_start < last_end:
                # Merge overlapping intervals by extending the end point
                merged[-1] = (last_start, max(last_end, current_end))
            else:
                merged.append((current_start, current_end))

        # Calculate total length of the merged intervals
        total_length = sum(end - start for start, end in merged)
        return total_length

    def _find_dependent_prompt(prompt: str, generations: List[str]) -> int:
        """
        Find the total length of the prompt that comes from upstream generations
        by finding the longest common substring and merging overlapping parts.
        """
        if not generations or not prompt:
            return 0

        min_size = 10  # Consider matches of 10 characters or more to be significant
        found_intervals = []

        # Use SequenceMatcher for efficient longest-match finding
        seq_matcher = SequenceMatcher(a=prompt)
        for gen in generations:
            if not gen:
                continue
            seq_matcher.set_seq2(gen)
            match = seq_matcher.find_longest_match(0, len(prompt), 0, len(gen))

            if match.size >= min_size:
                found_intervals.append((match.a, match.a + match.size))

        return _merge_intervals(found_intervals)

    def _calculate_dependency_for_group(group: DataFrame) -> DataFrame:
        """Apply function to calculate dependency for a single request_id group."""
        group = group.sort_values("request_time").reset_index(drop=True)
        dependent_lengths = []

        for idx, row in group.iterrows():
            task_name = row["task_name"]
            prompt = str(row["prompt"])
            dependent_length = 0

            # Use custom extractor if provided
            if extractors and task_name in extractors:
                # Assuming extractor returns the dependent prompt string
                dependent_prompt = extractors[task_name](
                    prompt, group["generation"].tolist()
                )
                dependent_length = len(dependent_prompt)
            else:
                # Automatic extraction: compare with generations from completed tasks
                other_gens = group.loc[
                    group["response_time"] <= row["request_time"], "generation"
                ].tolist()
                # Exclude the generation of the current task if it exists
                if row["generation"] in other_gens:
                    other_gens.remove(row["generation"])

                dependent_length = _find_dependent_prompt(prompt, other_gens)

            dependent_lengths.append(dependent_length)

        group["dependent_prompt_length"] = dependent_lengths
        return group

    # Main processing logic
    data_copy = data.copy()

    # Ensure prompt column is of string type to prevent errors
    data_copy["prompt"] = data_copy["prompt"].astype(str)

    tqdm.pandas(desc="Processing requests")
    # Apply the efficient calculation to each group
    processed_data = data_copy.groupby("request_id").progress_apply(
        _calculate_dependency_for_group
    )
    processed_data = processed_data.reset_index(drop=True)

    # Calculate dependent prompt fraction
    processed_data["prompt_length"] = processed_data["prompt"].str.len()
    processed_data["dependent_prompt_fraction"] = (
        processed_data["dependent_prompt_length"] / processed_data["prompt_length"]
    ).fillna(0)

    # Ensure fraction is between 0 and 1
    processed_data["dependent_prompt_fraction"].clip(0, 1, inplace=True)

    # Save processed data for debugging if requested
    if kwargs.get("save_processed_data", False):
        os.makedirs(save_dir, exist_ok=True)
        cache_dir = os.path.join(save_dir, ".cache")
        os.makedirs(cache_dir, exist_ok=True)
        processed_data_path = f"{cache_dir}/{tag}_processed_data.csv"
        processed_data.to_csv(processed_data_path, index=False)
        print(f"Saved processed data to {processed_data_path}")

    # Plotting section
    plot_data = shorten_task_name(processed_data)
    task_names = sorted(plot_data["task_name"].unique())
    n_tasks = len(task_names)

    if n_tasks == 0:
        print("No tasks to plot.")
        return ""

    # Setup subplot grid
    ncols = 2 if n_tasks > 2 else 1
    if n_tasks >= 6:
        ncols = 3
    if n_tasks >= 12:
        ncols = 4
    nrows = (n_tasks + ncols - 1) // ncols

    fig, axes = plt.subplots(
        nrows, ncols, figsize=(6 * ncols, 4 * nrows), squeeze=False
    )
    axes = axes.flatten()

    for i, task in enumerate(task_names):
        task_data = plot_data[plot_data["task_name"] == task]
        ax = axes[i]

        ax.hist(
            task_data["dependent_prompt_fraction"],
            bins=20,
            alpha=0.75,
            edgecolor="black",
            range=(0, 1),  # Fractions are between 0 and 1
        )
        ax.set_xlabel("Dependent Prompt Fraction")
        ax.set_ylabel("Frequency")
        ax.set_title(f"Task: {task}", fontsize=12)
        ax.grid(True, which="both", linestyle="--", linewidth=0.5)

        mean_frac = task_data["dependent_prompt_fraction"].mean()
        ax.axvline(
            mean_frac, color="red", linestyle="--", label=f"Mean: {mean_frac:.3f}"
        )
        ax.legend()

    # Hide unused subplots
    for j in range(i + 1, len(axes)):
        axes[j].set_visible(False)

    fig.suptitle(f"Dependent Prompt Distribution by Task - {tag}", fontsize=16, y=1.0)
    fig.tight_layout(rect=[0, 0, 1, 0.98])

    # Save the figure
    os.makedirs(save_dir, exist_ok=True)
    fig_path = os.path.join(save_dir, f"{tag}_prompt_dependency_dist.png")
    plt.savefig(fig_path, dpi=300, bbox_inches="tight")
    plt.close(fig)

    print(f"Saved prompt dependency distribution plot to {fig_path}")
    return fig_path


def calculate_predictability(
    data: DataFrame,
    tag: str = "test_agent",
    save_dir: str = "./plots",
    **kwargs: Dict[str, str],
) -> str:
    """
    Calculate the predictability of LLM generation length based on prompt length.

    Args:
        data: pd.DataFrame with required columns.
        tag: Tag for the plot, often the agent's name.
        save_dir: Directory to save the plot.
    Return:
        Path to the saved figure.
    """
    required_cols = ["task_name", "prompt_length", "generation_length"]
    if not all(col in data.columns for col in required_cols):
        missing_cols = [col for col in required_cols if col not in data.columns]
        raise ValueError(f"Missing required columns: {missing_cols}")

    # Build a Linear Regression model between prompt_length and generation_length for each task
    # calculate the predictability as the R^2 score of the model
    predictability_scores = {}
    for task in data["task_name"].unique():
        task_data = data[data["task_name"] == task]
        X = task_data[["prompt_length"]]
        y = task_data["generation_length"]
        model = LinearRegression()
        model.fit(X, y)
        r_squared = model.score(X, y)
        predictability_scores[task] = r_squared

    print(pd.DataFrame(predictability_scores.items(), columns=["task_name", "r_squared"]))
    # Plotting the predictability scores
    plt.figure(figsize=(10, 6))
    sns.barplot(
        x=list(predictability_scores.keys()), y=list(predictability_scores.values())
    )
    plt.title(f"Predictability Scores by Task - {tag}")
    plt.xlabel("Task")
    plt.ylabel("R^2 Score")
    plt.xticks(rotation=45)
    plt.grid(True)

    # Save the figure
    os.makedirs(save_dir, exist_ok=True)
    fig_path = f"{save_dir}/{tag}_predictability_scores.png"
    plt.tight_layout()
    plt.savefig(fig_path, dpi=300, bbox_inches="tight")
    plt.close()

    print(f"Saved predictability scores plot to {fig_path}")
    return fig_path


def get_args():
    import argparse

    parser = argparse.ArgumentParser(
        description="Plot LLM prompt generation distribution"
    )
    parser.add_argument(
        "--tag", type=str, default="test_agent", help="Tag for the agent or task"
    )
    parser.add_argument(
        "--vllm_metrics_dirs",
        type=str,
        nargs="+",
        required=True,
        help="Directories containing vLLM metrics",
    )
    parser.add_argument(
        "--agent_metrics_files",
        nargs="+",
        required=True,
        help="List of agent metrics files",
    )
    parser.add_argument(
        "--plots", type=str, nargs="+", default=None, help="List of plots to generate"
    )
    parser.add_argument(
        "--save_dir",
        type=str,
        default="./plots",
        help="Directory to save the plots",
    )
    return parser.parse_args()


if __name__ == "__main__":
    args = get_args()
    res = parse_llm_call_metrics(
        tag=args.tag,
        vllm_metrics_dirs=args.vllm_metrics_dirs,
        agent_metrics_files=args.agent_metrics_files,
    )

    print(len(res))
    print(res.head())

    if args.plots is None:
        args.plots = [
            "prompt_gen_length_dist",
            "prompt_sharing_dist",
            "prompt_dependency_dist",
            "predictability",
        ]

    if "prompt_gen_length_dist" in args.plots:
        plot_prompt_gen_length_dist(
            res,
            tag=args.tag,
            save_dir=args.save_dir,
            log_scale=True,
            plot_type="scatter",
        )
    if "prompt_sharing_dist" in args.plots:
        plot_prompt_sharing_dist(
            res,
            tag=args.tag,
            save_dir=args.save_dir,
            log_scale=True,
            show_breakdown=True,
        )
    if "prompt_dependency_dist" in args.plots:
        plot_prompt_dependency_dist(
            res,
            tag=args.tag,
            save_dir=args.save_dir,
            log_scale=True,
            save_processed_data=True,
        )

    if "predictability" in args.plots:
        calculate_predictability(
            res,
            tag=args.tag,
            save_dir=args.save_dir,
        )
