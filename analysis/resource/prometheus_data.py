import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import argparse
import os
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import glob
import requests
import sys
import logging

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


# Integrated Prometheus client functionality
class PrometheusClient:
    def __init__(self, base_url: str = "http://localhost:9090"):
        self.base_url = base_url.rstrip("/")
        self.api_url = f"{self.base_url}/api/v1"

    def query_range(
        self, query: str, start: datetime, end: datetime, step: str = "15s"
    ) -> Dict:
        """Query Prometheus for a range of data"""
        params = {
            "query": query,
            "start": start.timestamp(),
            "end": end.timestamp(),
            "step": step,
        }

        response = requests.get(f"{self.api_url}/query_range", params=params)
        response.raise_for_status()
        return response.json()

    def query(self, query: str) -> Dict:
        """Query Prometheus for current data"""
        params = {"query": query}
        response = requests.get(f"{self.api_url}/query", params=params)
        response.raise_for_status()
        return response.json()


def get_resource_queries() -> Dict[str, str]:
    """Define Prometheus queries for different resource metrics"""
    return {
        # GPU metrics (from dcgm-exporter)
        "gpu_memory_used": "DCGM_FI_DEV_MEM_COPY_UTIL",
        "gpu_utilization": "DCGM_FI_DEV_GPU_UTIL",
        "gpu_memory_total": "DCGM_FI_DEV_FB_TOTAL",
        "gpu_memory_free": "DCGM_FI_DEV_FB_FREE",
        # Container CPU usage (from cAdvisor)
        "container_cpu_usage": "rate(container_cpu_usage_seconds_total[1m])",
        "container_memory_usage": "container_memory_usage_bytes",
        "container_memory_limit": "container_spec_memory_limit_bytes",
        # Network usage
        "container_network_rx": "rate(container_network_receive_bytes_total[1m])",
        "container_network_tx": "rate(container_network_transmit_bytes_total[1m])",
        # Disk usage
        "container_fs_usage": "container_fs_usage_bytes",
        "container_fs_limit": "container_fs_limit_bytes",
        # System-wide metrics
        "node_cpu_usage": '100 - (avg by (instance) (rate(node_cpu_seconds_total{mode="idle"}[1m])) * 100)',
        "node_memory_usage": "node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes",
        "node_memory_total": "node_memory_MemTotal_bytes",
    }


def parse_prometheus_response_optimized(
    response: Dict, metric_name: str
) -> pd.DataFrame:
    """Parse Prometheus response into DataFrame with optimized columns"""
    if response["status"] != "success":
        raise ValueError(f"Prometheus query failed: {response}")

    data = response["data"]["result"]
    if not data:
        return pd.DataFrame()

    rows = []
    for series in data:
        labels = series["metric"]

        # Extract only essential labels based on metric type
        essential_labels = {}
        if metric_name.startswith("gpu_"):
            # For GPU metrics, keep gpu identifier
            if "gpu" in labels:
                essential_labels["gpu"] = labels["gpu"]
            elif "UUID" in labels:
                essential_labels["gpu"] = labels["UUID"]
            elif "device" in labels:
                essential_labels["gpu"] = labels["device"]
        elif metric_name.startswith("container_"):
            # For container metrics, keep container identification
            if "name" in labels:
                essential_labels["name"] = labels["name"]
            if "id" in labels:
                essential_labels["id"] = labels["id"]
            if "container_label_com_docker_compose_service" in labels:
                essential_labels["service"] = labels[
                    "container_label_com_docker_compose_service"
                ]
        elif metric_name.startswith("node_"):
            # For node metrics, keep instance identifier
            if "instance" in labels:
                essential_labels["instance"] = labels["instance"]

        for timestamp, value in series["values"]:
            row = {
                "timestamp": pd.to_datetime(float(timestamp), unit="s"),
                "value": float(value),
                "metric": metric_name,
                **essential_labels,
            }
            rows.append(row)

    return pd.DataFrame(rows)


def collect_prometheus_data(
    prometheus_url: str,
    start_time: datetime,
    end_time: datetime,
    step: str,
    output_dir: str,
    tag: str,
) -> bool:
    """Collect data from Prometheus and save to CSV files"""
    try:
        print(f"Collecting Prometheus data from {start_time} to {end_time}")
        print(f"Prometheus URL: {prometheus_url}")
        print(f"Step interval: {step}")
        print("-" * 50)

        # Initialize client and fetch data
        client = PrometheusClient(prometheus_url)
        queries = get_resource_queries()
        results = {}

        for metric_name, query in queries.items():
            print(f"Fetching {metric_name}...")
            try:
                response = client.query_range(query, start_time, end_time, step)
                df = parse_prometheus_response_optimized(response, metric_name)
                if not df.empty:
                    results[metric_name] = df
                    print(f"  ✓ Retrieved {len(df)} data points")
                else:
                    print(f"  ⚠ Empty data for {metric_name}")
            except Exception as e:
                print(f"  ✗ Error fetching {metric_name}: {e}")

        # Save data
        os.makedirs(output_dir, exist_ok=True)

        # Save individual metric files
        for metric_name, df in results.items():
            filename = f"{tag}_{metric_name}.csv"
            filepath = os.path.join(output_dir, filename)
            df.to_csv(filepath, index=False)
            print(f"Saved {metric_name} to {filepath} ({len(df)} rows)")

        # Save combined data
        if results:
            combined_df = pd.concat(results.values(), ignore_index=True)
            combined_filepath = os.path.join(output_dir, f"{tag}_combined_metrics.csv")
            combined_df.to_csv(combined_filepath, index=False)
            print(
                f"Saved combined data to {combined_filepath} ({len(combined_df)} total rows)"
            )

            # Print summary statistics
            print(f"\nData Collection Summary:")
            print(f"  Total metrics collected: {len(results)}")
            print(f"  Total data points: {len(combined_df)}")
            print(
                f"  Time range: {combined_df['timestamp'].min()} to {combined_df['timestamp'].max()}"
            )
        else:
            print("No data collected!")
            return False

        return True

    except requests.exceptions.ConnectionError:
        print(f"✗ Failed to connect to Prometheus at {prometheus_url}")
        print("  Please check if Prometheus is running and accessible")
        return False
    except Exception as e:
        print(f"✗ Error during data collection: {e}")
        return False


def load_prometheus_data(data_dir: str, tag: str) -> Dict[str, pd.DataFrame]:
    """Load Prometheus data from CSV files"""
    data = {}
    pattern = os.path.join(data_dir, f"{tag}_*.csv")
    files = glob.glob(pattern)

    if not files:
        print(f"No data files found matching pattern: {pattern}")
        return data

    for file_path in files:
        filename = os.path.basename(file_path)
        # Extract metric name from filename: tag_metric_name.csv
        metric_name = filename.replace(f"{tag}_", "").replace(".csv", "")
        if metric_name != "combined_metrics":
            try:
                df = pd.read_csv(file_path)
                df["timestamp"] = pd.to_datetime(df["timestamp"])
                data[metric_name] = df
                print(f"Loaded {metric_name}: {len(df)} data points")
            except Exception as e:
                print(f"Error loading {file_path}: {e}")

    return data


def main():
    parser = argparse.ArgumentParser(
        description="Fetch Prometheus data for resource analysis"
    )
    parser.add_argument(
        "--prometheus-url",
        default="http://localhost:9090",
        help="Prometheus server URL",
    )
    parser.add_argument(
        "--start-time",
        required=True,
        help="Start time (ISO format: 2024-01-01T00:00:00)",
    )
    parser.add_argument(
        "--end-time", required=True, help="End time (ISO format: 2024-01-01T01:00:00)"
    )
    parser.add_argument("--step", default="15s", help="Query step interval")
    parser.add_argument(
        "--output-dir",
        default="./prometheus_data",
        help="Output directory for data files",
    )
    parser.add_argument("--tag", required=True, help="Tag for output files")
    args = parser.parse_args()

    # Parse timestamps
    start_time = datetime.fromisoformat(args.start_time)
    end_time = datetime.fromisoformat(args.end_time)

    print(f"Collecting Prometheus data from {start_time} to {end_time}")
    print(f"Prometheus URL: {args.prometheus_url}")
    print(f"Step interval: {args.step}")
    print("-" * 50)

    collect_prometheus_data(
        args.prometheus_url, start_time, end_time, args.step, args.output_dir, args.tag
    )
    data = load_prometheus_data(args.output_dir, args.tag)

    print(f"\nData collection complete. {len(data)} metrics saved.")


if __name__ == "__main__":
    main()
